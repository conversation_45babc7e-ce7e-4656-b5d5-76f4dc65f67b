import 'package:flutter/material.dart';
import '../../services/service_locator.dart';
import 'login_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => AuthWrapperState();
}

class AuthWrapperState extends State<AuthWrapper> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: StreamBuilder(
        stream: serviceLocator.authService.client.auth.onAuthStateChange,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          if (snapshot.hasData && snapshot.data?.session != null) {
            // User is authenticated, navigate to dashboard
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Navigator.of(context).pushReplacementNamed('/dashboard');
            });
            
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          // User is not authenticated, show login screen
          return const LoginScreen();
        },
      ),
    );
  }
} 