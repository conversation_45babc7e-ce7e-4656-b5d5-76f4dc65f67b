import 'package:uuid/uuid.dart';

enum ActivityType {
  login,
  logout,
  propertyCreated,
  propertyUpdated,
  propertyDeleted,
  roomCreated,
  roomUpdated,
  roomDeleted,
  tenantCreated,
  tenantUpdated,
  tenantDeleted,
  roomAssignment,
  vacateNotice,
  tenantMovedOut,
  maintenanceRequest,
  maintenanceCompleted,
  paymentReceived,
  paymentRefunded,
  documentUploaded,
  documentDeleted,
  messageReceived,
  messageSent,
  other
}

class ActivityLog {
  final String id;
  final ActivityType type;
  final String? userId;
  final String? tenantId;
  final String? roomId;
  final String? propertyId;
  final String action;
  final Map<String, dynamic>? details;
  final DateTime createdAt;

  ActivityLog({
    String? id,
    required this.type,
    this.userId,
    this.tenantId,
    this.roomId,
    this.propertyId,
    required this.action,
    this.details,
    DateTime? createdAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  // Create ActivityLog from JSON
  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    return ActivityLog(
      id: json['id'],
      type: _parseActivityType(json['type']),
      userId: json['user_id'],
      tenantId: json['tenant_id'],
      roomId: json['room_id'],
      propertyId: json['property_id'],
      action: json['action'],
      details: json['details'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  // Convert ActivityLog to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'user_id': userId,
      'tenant_id': tenantId,
      'room_id': roomId,
      'property_id': propertyId,
      'action': action,
      'details': details,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Parse activity type from string
  static ActivityType _parseActivityType(String? type) {
    if (type == null) return ActivityType.other;
    
    switch (type) {
      case 'roomAssignment':
        return ActivityType.roomAssignment;
      case 'tenantCreation':
        return ActivityType.tenantCreated;
      case 'tenantUpdate':
        return ActivityType.tenantUpdated;
      case 'roomUpdate':
        return ActivityType.roomUpdated;
      case 'propertyUpdate':
        return ActivityType.propertyUpdated;
      case 'vacateNotice':
        return ActivityType.vacateNotice;
      case 'tenantMovedOut':
        return ActivityType.tenantMovedOut;
      case 'maintenanceRequest':
        return ActivityType.maintenanceRequest;
      case 'maintenanceCompleted':
        return ActivityType.maintenanceCompleted;
      case 'paymentReceived':
        return ActivityType.paymentReceived;
      case 'paymentRefunded':
        return ActivityType.paymentRefunded;
      case 'documentUploaded':
        return ActivityType.documentUploaded;
      case 'documentDeleted':
        return ActivityType.documentDeleted;
      case 'messageReceived':
        return ActivityType.messageReceived;
      case 'messageSent':
        return ActivityType.messageSent;
      case 'other':
      default:
        return ActivityType.other;
    }
  }
}

class ActivityLogEntry {
  final String id;
  final String action;
  final String description;
  final DateTime createdAt;
  final Map<String, dynamic>? changes;
  final String? userId;
  final String entityId;
  final String entityType;

  ActivityLogEntry({
    required this.id,
    required this.action,
    required this.description,
    required this.createdAt,
    this.changes,
    this.userId,
    required this.entityId,
    required this.entityType,
  });

  factory ActivityLogEntry.fromJson(Map<String, dynamic> json) {
    return ActivityLogEntry(
      id: json['id'] as String,
      action: json['action'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      changes: json['changes'] as Map<String, dynamic>?,
      userId: json['user_id'] as String?,
      entityId: json['entity_id'] as String,
      entityType: json['entity_type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'action': action,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'changes': changes,
      'user_id': userId,
      'entity_id': entityId,
      'entity_type': entityType,
    };
  }
}
