import 'package:flutter/material.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../services/service_locator.dart';
import 'room_form_page.dart';
import '../../utils/currency_formatter.dart';
import '../../models/tenant/tenant.dart';
import '../../screens/tenants/tenant_details_page.dart';
import '../../screens/tenants/vacate_room_page.dart';
import '../../screens/tenants/tenant_selection_page.dart';
import '../../widgets/error_dialog.dart';

class RoomDetailPage extends StatefulWidget {
  final String roomId;

  const RoomDetailPage({super.key, required this.roomId});

  @override
  State<RoomDetailPage> createState() => _RoomDetailPageState();
}

class _RoomDetailPageState extends State<RoomDetailPage> {
  Room? _room;
  Property? _property;
  bool _isLoading = true;
  bool _isLoadingTenant = false;
  List<Tenant> _tenants = [];

  @override
  void initState() {
    super.initState();
    _loadRoomData();
  }

  Future<void> _loadRoomData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get room from service
      final room = await serviceLocator.roomService.getRoomById(widget.roomId);

      if (room != null) {
        // Get property this room belongs to
        final property = await serviceLocator.propertyService.getPropertyById(
          room.propertyId,
        );

        if (mounted) {
          setState(() {
            _room = room;
            _property = property;
            _isLoading = false;
          });

          // Load tenant data if room is occupied
          if (room.occupancyStatus == RoomOccupancyStatus.occupied) {
            _loadTenantData();
          }
        }
      } else {
        // Room not found, go back
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Room not found'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: Colors.red,
            content: Text('Error: ${e.toString()}'),
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  Future<void> _loadTenantData() async {
    if (_room == null) return;
    
    setState(() {
      _isLoadingTenant = true;
    });
    
    try {
      final tenants = await serviceLocator.tenantService.getTenantsByRoomId(_room!.id);
      
      if (mounted) {
        setState(() {
          _tenants = tenants;
          _isLoadingTenant = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingTenant = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading tenant data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _assignTenant() async {
    if (_room == null) return;
    
    // Capture context dependent variables before the async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);
    
    try {
      // Navigate to tenant selection page
      final result = await navigator.push(
        MaterialPageRoute(
          builder: (context) => TenantSelectionPage(
            roomId: _room!.id,
            roomName: _room!.name,
          ),
        ),
      );
      
      if (result != null && mounted) {
        // Refresh room data
        _loadRoomData();
        
        // Show success notification
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Tenant assigned successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Show error dialog with details
        ErrorDialog.show(
          context,
          title: 'Error Assigning Tenant',
          message: 'There was an error while trying to assign a tenant.',
          details: e.toString(),
        );
      }
    }
  }

  Future<void> _vacateRoom() async {
    if (_room == null || _tenants.isEmpty) return;
    
    // Capture context-dependent variables before the async gap.
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    // Verify tenant still exists in database before proceeding
    final tenant = _tenants.first;
    final existingTenant = await serviceLocator.tenantService.getTenantById(tenant.id);
    if (existingTenant == null) {
      if (mounted) {
        // Show the custom error dialog
        ErrorDialog.showTenantNotFound(
          context,
          tenantId: tenant.id,
          onDismiss: () {
            // Refresh room data to update UI
            _loadRoomData();
          },
        );
      }
      return;
    }
    
    // Navigate to vacate room page
    final result = await navigator.push(
      MaterialPageRoute(
        builder: (context) => VacateRoomPage(
          tenant: existingTenant, // Use the verified tenant
          roomName: _room!.name,
        ),
      ),
    );
    
    if (result != null) {
      // Refresh room data
      _loadRoomData();
      
      // Show success notification
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Room vacated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _changeTenant() async {
    if (_room == null) return;
    
    // Capture context dependent variables before the async gap.
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    // Show dialog to confirm
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Tenant'),
        content: const Text('This will vacate the current tenant and allow you to assign a new tenant. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('CONTINUE'),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Update room status to vacant
      await serviceLocator.roomService.updateRoomOccupancyStatus(
        _room!.id,
        RoomOccupancyStatus.vacant,
      );
      
      // Update tenant if exists
      if (_tenants.isNotEmpty) {
        final tenant = _tenants.first;
        
        // Verify tenant still exists in database before updating
        final existingTenant = await serviceLocator.tenantService.getTenantById(tenant.id);
        if (existingTenant != null) {
          final updatedTenant = existingTenant.copyWith(
            roomId: null,
            status: TenantStatus.movedOut,
            updatedAt: DateTime.now(),
          );
          
          await serviceLocator.tenantService.updateTenant(updatedTenant);
        } else {
          // Show error dialog for tenant not found
          if (mounted) {
            ErrorDialog.showTenantNotFound(
              context,
              tenantId: tenant.id,
              onDismiss: null,
            );
          }
          // Log that tenant wasn't found
          debugPrint('Warning: Tenant with ID ${tenant.id} not found during room change');
        }
      }
      
      // Reload room data
      await _loadRoomData();
      
      if (!mounted) return;
      
      // Navigate to tenant assignment page
      final result = await navigator.push(
        MaterialPageRoute(
          builder: (context) => TenantSelectionPage(
            roomId: _room!.id,
            roomName: _room!.name,
          ),
        ),
      );
      
      if (result != null && mounted) {
        // Refresh room data
        _loadRoomData();
        
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Tenant changed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // Show error dialog with details
        ErrorDialog.show(
          context,
          title: 'Error Changing Tenant',
          message: 'There was an error while trying to change the tenant.',
          details: e.toString(),
          onRetry: _changeTenant,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Room Details'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    // Get color based on occupancy status
    Color statusColor;
    switch (_room!.occupancyStatus) {
      case RoomOccupancyStatus.vacant:
        statusColor = Colors.green;
        break;
      case RoomOccupancyStatus.occupied:
        statusColor = Colors.blue;
        break;
      case RoomOccupancyStatus.reserved:
        statusColor = Colors.orange;
        break;
      case RoomOccupancyStatus.maintenance:
        statusColor = Colors.red;
        break;
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_room!.name),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit Room',
            onPressed: _editRoom,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'Delete Room',
            onPressed: _deleteRoom,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Room header with image, name, property and status
            Stack(
              children: [
                // Room image or placeholder
                SizedBox(
                  height: 150,
                  width: double.infinity,
                  child:
                      _room!.imageUrl != null && _room!.imageUrl!.isNotEmpty
                          ? Image.network(
                            _room!.imageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder:
                                (context, error, stackTrace) => Container(
                                  color: Colors.grey[300],
                                  child: const Icon(
                                    Icons.meeting_room,
                                    size: 60,
                                  ),
                                ),
                          )
                          : Container(
                            color: Colors.grey[300],
                            child: const Icon(Icons.meeting_room, size: 60),
                          ),
                ),

                // Overlay with gradient for text visibility
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        colors: [
                          Colors.black.withAlpha(179),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _room!.name,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                offset: Offset(1, 1),
                                blurRadius: 3,
                                color: Colors.black45,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Status badge in top right corner
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(230),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      _room!.occupancyStatus.toString().split('.').last,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Property link and key details
            Container(
              color: Colors.grey[100],
              padding: const EdgeInsets.all(12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Property link on the left side
                  if (_property != null)
                    Expanded(
                      child: InkWell(
                        onTap: () => Navigator.pop(context),
                        child: Row(
                          children: [
                            Icon(
                              Icons.home,
                              size: 18,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _property!.name,
                                style: TextStyle(
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Room type
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Icon(
                          Icons.category,
                          size: 18,
                          color: Colors.amber.shade800,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _room!.roomTypeName,
                          style: TextStyle(
                            color: Colors.grey[800],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Price and key details card
            Card(
              margin: const EdgeInsets.all(12),
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Container(
                      padding: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Room Details',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Key details in a grid layout
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Rental Price - Full width
                        ListTile(
                          leading: const Icon(Icons.attach_money),
                          title: const Text('Rental Price'),
                          subtitle: Text(
                            CurrencyFormatter.formatAmountWithCode(_room!.rentalPrice),
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Other details in a row
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Furnished status
                            Expanded(
                              child: _buildCompactDetailItem(
                                'Furnished',
                                _room!.isFurnished ? 'Yes' : 'No',
                                _room!.isFurnished
                                    ? Icons.check_circle
                                    : Icons.cancel,
                                _room!.isFurnished ? Colors.green : Colors.red,
                              ),
                            ),
                            
                            // Size if available
                            if (_room!.size != null)
                              Expanded(
                                child: _buildCompactDetailItem(
                                  'Size',
                                  '${_room!.size!.toStringAsFixed(0)} sq ft',
                                  Icons.square_foot,
                                  Colors.blue.shade700,
                                ),
                              ),
                              
                            // Floor if available
                            if (_room!.floor != null)
                              Expanded(
                                child: _buildCompactDetailItem(
                                  'Floor',
                                  '${_room!.floor}',
                                  Icons.stairs,
                                  Colors.purple.shade700,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Description and notes
            if (_room!.description != null && _room!.description!.isNotEmpty ||
                _room!.notes != null && _room!.notes!.isNotEmpty)
              Card(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                elevation: 1,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_room!.description != null &&
                          _room!.description!.isNotEmpty) ...[
                        const Text(
                          'Description',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 15,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _room!.description!,
                          style: TextStyle(color: Colors.grey[800]),
                        ),
                      ],
                      if (_room!.notes != null && _room!.notes!.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        const Text(
                          'Notes',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 15,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          _room!.notes!,
                          style: TextStyle(color: Colors.grey[800]),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

            // Amenities
            if (_room!.amenities.isNotEmpty)
              Card(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                elevation: 1,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section header
                      Container(
                        padding: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.star_outline, color: Colors.amber),
                            const SizedBox(width: 8),
                            const Text(
                              'Amenities',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Amenities in a wrap
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            _room!.amenities.map((amenity) {
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.blue.shade100,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      _getAmenityIcon(amenity.displayName),
                                      size: 14,
                                      color: Colors.blue.shade700,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      amenity.displayName,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: Colors.blue.shade900,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                      ),
                    ],
                  ),
                ),
              ),

            // Current tenant card (if occupied)
            if (_room!.occupancyStatus == RoomOccupancyStatus.occupied)
              Card(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section header
                      Container(
                        padding: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.person, color: Colors.indigo),
                            const SizedBox(width: 8),
                            const Text(
                              'Current Tenant',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      // Tenant details or loading indicator
                      _isLoadingTenant
                          ? const Center(
                              child: Padding(
                                padding: EdgeInsets.all(16.0),
                                child: CircularProgressIndicator(),
                              ),
                            )
                          : _tenants.isEmpty
                              ? const Text(
                                  'No tenant information found',
                                  style: TextStyle(fontStyle: FontStyle.italic),
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Tenant name
                                    Row(
                                      children: [
                                        const Icon(Icons.person_outline, size: 18, color: Colors.blue),
                                        const SizedBox(width: 8),
                                        Text(
                                          '${_tenants.first.firstName} ${_tenants.first.lastName}',
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    
                                    // Contact info
                                    Row(
                                      children: [
                                        const Icon(Icons.email, size: 18, color: Colors.blue),
                                        const SizedBox(width: 8),
                                        Text(
                                          _tenants.first.email,
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ],
                                    ),
                                    
                                    if (_tenants.first.phoneNumber != null) ...[
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Icon(Icons.phone, size: 18, color: Colors.blue),
                                          const SizedBox(width: 8),
                                          Text(
                                            _tenants.first.phoneNumber!,
                                            style: const TextStyle(fontSize: 14),
                                          ),
                                        ],
                                      ),
                                    ],
                                    
                                    const SizedBox(height: 12),
                                    
                                    // Lease dates
                                    if (_tenants.first.leaseStartDate != null) ...[
                                      Row(
                                        children: [
                                          const Icon(Icons.calendar_today, size: 18, color: Colors.blue),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Lease Start: ${_tenants.first.leaseStartDate!.toString().split(' ')[0]}',
                                            style: const TextStyle(fontSize: 14),
                                          ),
                                        ],
                                      ),
                                    ],
                                    
                                    if (_tenants.first.leaseEndDate != null) ...[
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Icon(Icons.event_busy, size: 18, color: Colors.blue),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Lease End: ${_tenants.first.leaseEndDate!.toString().split(' ')[0]}',
                                            style: const TextStyle(fontSize: 14),
                                          ),
                                        ],
                                      ),
                                      
                                      // Calculate days remaining in lease
                                      const SizedBox(height: 8),
                                      Builder(
                                        builder: (context) {
                                          final now = DateTime.now();
                                          final end = _tenants.first.leaseEndDate!;
                                          final daysRemaining = end.difference(now).inDays;
                                          
                                          Color textColor;
                                          String message;
                                          
                                          if (daysRemaining < 0) {
                                            textColor = Colors.red;
                                            message = 'Lease expired ${-daysRemaining} days ago';
                                          } else if (daysRemaining < 30) {
                                            textColor = Colors.orange;
                                            message = '$daysRemaining days remaining (expiring soon)';
                                          } else {
                                            textColor = Colors.green;
                                            message = '$daysRemaining days remaining';
                                          }
                                          
                                          return Row(
                                            children: [
                                              const Icon(Icons.timelapse, size: 18, color: Colors.blue),
                                              const SizedBox(width: 8),
                                              Text(
                                                message,
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.bold,
                                                  color: textColor,
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ],
                                    
                                    const SizedBox(height: 16),
                                    
                                    // View tenant details button
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        ElevatedButton.icon(
                                          onPressed: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) => TenantDetailsPage(
                                                  tenant: _tenants.first,
                                                ),
                                              ),
                                            );
                                          },
                                          icon: const Icon(Icons.visibility, size: 16),
                                          label: const Text('View Details'),
                                          style: ElevatedButton.styleFrom(
                                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                    ],
                  ),
                ),
              ),

            // Tenant management buttons
            Card(
              margin: const EdgeInsets.all(12),
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Container(
                      padding: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.people_alt, color: Colors.deepPurple),
                          const SizedBox(width: 8),
                          const Text(
                            'Tenant Management',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Action buttons based on room status
                    if (_room!.occupancyStatus == RoomOccupancyStatus.vacant) ...[
                      // Assign tenant button
                      ElevatedButton.icon(
                        onPressed: _assignTenant,
                        icon: const Icon(Icons.person_add),
                        label: const Text('Assign Tenant'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          minimumSize: const Size(double.infinity, 0),
                        ),
                      ),
                    ] else if (_room!.occupancyStatus == RoomOccupancyStatus.occupied) ...[
                      // Change tenant and vacate room buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _changeTenant,
                              icon: const Icon(Icons.swap_horiz),
                              label: const Text('Change Tenant'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _vacateRoom,
                              icon: const Icon(Icons.exit_to_app),
                              label: const Text('Vacate Room'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      // For reserved or maintenance status
                      ElevatedButton.icon(
                        onPressed: _assignTenant,
                        icon: const Icon(Icons.person_add),
                        label: const Text('Assign Tenant'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          minimumSize: const Size(double.infinity, 0),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Change occupancy status
            Card(
              margin: const EdgeInsets.all(12),
              elevation: 1,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Container(
                      padding: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey.shade300,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.update, color: Colors.deepPurple),
                          const SizedBox(width: 8),
                          const Text(
                            'Change Status',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Status buttons - always show all options
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatusButton(
                            'Vacant',
                            RoomOccupancyStatus.vacant,
                            Colors.green,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatusButton(
                            'Occupied',
                            RoomOccupancyStatus.occupied,
                            Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatusButton(
                            'Reserved',
                            RoomOccupancyStatus.reserved,
                            Colors.orange,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatusButton(
                            'Maintenance',
                            RoomOccupancyStatus.maintenance,
                            Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Compact detail item with icon
  Widget _buildCompactDetailItem(
    String label,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withAlpha(30),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 16, color: iconColor),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to get amenity icons
  IconData _getAmenityIcon(String amenityName) {
    final name = amenityName.toLowerCase();
    if (name.contains('air') || name.contains('conditioning')) {
      return Icons.ac_unit;
    }
    if (name.contains('bathroom') || name.contains('toilet')) {
      return Icons.bathroom;
    }
    if (name.contains('kitchen')) {
      return Icons.kitchen;
    }
    if (name.contains('tv') || name.contains('television')) {
      return Icons.tv;
    }
    if (name.contains('wifi') || name.contains('internet')) {
      return Icons.wifi;
    }
    if (name.contains('laundry') || name.contains('washer')) {
      return Icons.local_laundry_service;
    }
    if (name.contains('parking')) {
      return Icons.local_parking;
    }
    return Icons.check_circle;
  }

  Widget _buildStatusButton(
    String label,
    RoomOccupancyStatus status,
    Color color,
  ) {
    final isCurrentStatus = _room!.occupancyStatus == status;

    return ElevatedButton(
      onPressed: isCurrentStatus ? null : () => _updateRoomStatus(status),
      style: ElevatedButton.styleFrom(
        backgroundColor: isCurrentStatus ? color : Colors.grey[200],
        foregroundColor: isCurrentStatus ? Colors.white : color,
        disabledBackgroundColor: color,
        disabledForegroundColor: Colors.white,
      ),
      child: Text(label),
    );
  }

  Future<void> _updateRoomStatus(RoomOccupancyStatus status) async {
    // Capture ScaffoldMessenger before the async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedRoom = await serviceLocator.roomService
          .updateRoomOccupancyStatus(_room!.id, status);

      if (mounted) {
        setState(() {
          _room = updatedRoom;
          _isLoading = false;
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              'Room status updated to ${status.toString().split('.').last}',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            backgroundColor: Colors.red,
            content: Text('Error: ${e.toString()}'),
          ),
        );
      }
    }
  }

  void _editRoom() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => RoomFormPage(room: _room)),
    );

    if (result == true) {
      _loadRoomData();
    }
  }

  void _deleteRoom() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Room'),
            content: Text('Are you sure you want to delete "${_room!.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  // Capture navigator and scaffold messenger before async gap
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  setState(() {
                    _isLoading = true;
                  });

                  try {
                    final success = await serviceLocator.roomService.deleteRoom(
                      _room!.id,
                    );

                    if (mounted) {
                      if (success) {
                        navigator.pop();
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(content: Text('Room deleted')),
                        );
                      } else {
                        setState(() {
                          _isLoading = false;
                        });

                        scaffoldMessenger.showSnackBar(
                          const SnackBar(
                            backgroundColor: Colors.red,
                            content: Text('Failed to delete room'),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });

                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          backgroundColor: Colors.red,
                          content: Text('Error: ${e.toString()}'),
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('DELETE'),
              ),
            ],
          ),
    );
  }
}
