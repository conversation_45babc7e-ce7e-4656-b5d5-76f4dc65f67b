import '../../utils/currency_formatter.dart';
import '../../services/service_locator.dart';

/// Represents a utility bill configuration for a property
class UtilityBill {
  String? id; // Database ID
  String name;
  double rate;
  String? unit;
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;

  UtilityBill({
    this.id,
    required this.name,
    required this.rate,
    this.unit,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a utility bill from JSON data
  factory UtilityBill.fromJson(Map<String, dynamic> json) {
    return UtilityBill(
      id: json['id'] as String?,
      name: json['name'] as String,
      rate: (json['rate'] as num).toDouble(),
      unit: json['unit'] as String?,
      notes: json['notes'] as String?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  /// Convert utility bill to JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'rate': rate,
      if (unit != null) 'unit': unit,
      if (notes != null) 'notes': notes,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }
  
  /// Get formatted rate with currency symbol
  String get formattedRate {
    return CurrencyFormatter.formatAmount(rate);
  }
  
  /// Get formatted rate with currency symbol and code
  String get formattedRateWithCode {
    return CurrencyFormatter.formatAmountWithCode(rate);
  }
}

/// Represents a property in the system
class Property {
  final String id;
  String name;
  String address;
  String city;
  String state;
  String zipCode;
  String? description;
  String? imageUrl;
  List<UtilityBill> utilityBills; // Utility bills for this property
  Map<String, dynamic>? additionalInfo;
  DateTime createdAt;
  DateTime updatedAt;

  Property({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    this.description,
    this.imageUrl,
    List<UtilityBill>? utilityBills,
    this.additionalInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    utilityBills = utilityBills ?? [],
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  /// Create a property from JSON data
  factory Property.fromJson(Map<String, dynamic> json) {
    return Property(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zip_code'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      utilityBills: (json['utility_bills'] as List<dynamic>?)
          ?.map((e) => UtilityBill.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      additionalInfo: json['additional_info'] as Map<String, dynamic>?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : DateTime.now(),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : DateTime.now(),
    );
  }

  /// Convert property to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'description': description,
      'image_url': imageUrl,
      'utility_bills': utilityBills.map((bill) => bill.toJson()).toList(),
      'additional_info': additionalInfo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy of this property with given fields replaced with new values
  Property copyWith({
    String? name,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? description,
    String? imageUrl,
    List<UtilityBill>? utilityBills,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Property(
      id: id,
      name: name ?? this.name,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      utilityBills: utilityBills ?? this.utilityBills,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is Property &&
      other.id == id &&
      other.name == name &&
      other.address == address &&
      other.city == city &&
      other.state == state &&
      other.zipCode == zipCode &&
      other.description == description &&
      other.imageUrl == imageUrl;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      address.hashCode ^
      city.hashCode ^
      state.hashCode ^
      zipCode.hashCode ^
      description.hashCode ^
      imageUrl.hashCode;
  }

  /// Calculate and format the total monthly income from all rooms in this property
  Future<String> getFormattedMonthlyIncome() async {
    try {
      // Get all rooms for this property
      final rooms = await serviceLocator.roomService.getRoomsByPropertyId(id);
      
      // Calculate total rental income
      double totalIncome = 0;
      for (final room in rooms) {
        totalIncome += room.rentalPrice;
      }
      
      // Format with currency
      return CurrencyFormatter.formatAmountWithCode(totalIncome);
    } catch (e) {
      return CurrencyFormatter.formatAmountWithCode(0);
    }
  }
} 