import '../../models/expense/expense_model.dart';
import '../../utils/logger.dart';
import '../supabase_service.dart';

class Logger {
  final String _name;
  
  Logger(this._name);
  
  void error(String message) {
    AppLogger.error('[$_name] $message');
  }
  
  void info(String message) {
    AppLogger.info('[$_name] $message');
  }
  
  void warning(String message) {
    AppLogger.warning('[$_name] $message');
  }
  
  void debug(String message) {
    AppLogger.debug('[$_name] $message');
  }
}

class ExpenseService {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger('ExpenseService');

  ExpenseService(this._supabaseService);

  Future<List<ExpenseModel>> getExpenses({
    String? propertyId,
    String? categoryId,
    String? roomId,
    DateTime? startDate,
    DateTime? endDate,
    bool useExpenseSummary = false,
  }) async {
    try {
      final supabase = _supabaseService.client;
      
      // For reporting purposes, we might need to use the detailed table when filtering by IDs
      // since the summary view doesn't include the ID columns
      final needsDetailedTable = (propertyId != null || categoryId != null || roomId != null);
      
      // Use expense_summary view for reporting when no ID filters are needed
      final tableName = (useExpenseSummary && !needsDetailedTable) ? 'expense_summary' : 'expenses';
      
      dynamic query;
      
      if (tableName == 'expense_summary') {
        // Using the summary view (no ID filters)
        query = supabase
            .from(tableName)
            .select();
            
        // Apply date filters
        if (startDate != null) {
          query = query.filter('date', 'gte', startDate.toIso8601String());
        }
        
        if (endDate != null) {
          query = query.filter('date', 'lte', endDate.toIso8601String());
        }
      } else {
        // Using the detailed expenses table with joins
        query = supabase
            .from('expenses')
            .select('''
              *,
              expense_categories!left(name, color),
              vendors!left(name),
              properties!left(name),
              rooms!left(name)
            ''');
            
        // Apply filters
        if (startDate != null) {
          query = query.filter('date', 'gte', startDate.toIso8601String());
        }
        
        if (endDate != null) {
          query = query.filter('date', 'lte', endDate.toIso8601String());
        }
        
        if (categoryId != null) {
          query = query.filter('category_id', 'eq', categoryId);
        }
        
        if (propertyId != null) {
          query = query.filter('property_id', 'eq', propertyId);
        }
        
        if (roomId != null) {
          query = query.filter('room_id', 'eq', roomId);
        }
      }
      
      // Apply ordering
      final response = await query.order('date', ascending: false);
      
      if (tableName == 'expense_summary') {
        // Summary view already has the joined data
        return response.map<ExpenseModel>((json) => ExpenseModel.fromJson(json)).toList();
      } else {
        // Need to map the joined data
        return response.map<ExpenseModel>((expense) {
          final categoryData = expense['expense_categories'];
          final vendorData = expense['vendors'];
          final propertyData = expense['properties'];
          final roomData = expense['rooms'];
          
          return ExpenseModel.fromJson({
            ...expense,
            'category_name': categoryData?['name'],
            'category_color': categoryData?['color'],
            'vendor_name': vendorData?['name'],
            'property_name': propertyData?['name'],
            'room_name': roomData?['name'],
          });
        }).toList();
      }
    } catch (e) {
      _logger.error('Error fetching expenses: $e');
      rethrow;
    }
  }

  Future<ExpenseModel?> getExpenseById(String id) async {
    try {
      final supabase = _supabaseService.client;

      final response = await supabase
          .from('expense_summary')
          .select()
          .filter('id', 'eq', id)
          .maybeSingle();

      if (response == null) {
        _logger.info('No expense found with ID: $id');
        return null;
      }

      return ExpenseModel.fromJson(response);
    } catch (e) {
      _logger.error('Error fetching expense by ID: $e');
      return null;
    }
  }

  Future<ExpenseModel> createExpense(ExpenseModel expense) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('expenses')
          .insert(expense.toJson())
          .select('''
            *,
            expense_categories!inner(name, color),
            vendors(name)
          ''')
          .single();
      
      final categoryData = response['expense_categories'];
      final vendorData = response['vendors'];
      
      return ExpenseModel.fromJson({
        ...response,
        'category_name': categoryData?['name'],
        'category_color': categoryData?['color'],
        'vendor_name': vendorData?['name'],
      });
    } catch (e) {
      _logger.error('Error creating expense: $e');
      rethrow;
    }
  }

  Future<ExpenseModel> updateExpense(ExpenseModel expense) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('expenses')
          .update(expense.toJson())
          .filter('id', 'eq', expense.id!)
          .select('''
            *,
            expense_categories!inner(name, color),
            vendors(name)
          ''')
          .single();
      
      final categoryData = response['expense_categories'];
      final vendorData = response['vendors'];
      
      return ExpenseModel.fromJson({
        ...response,
        'category_name': categoryData?['name'],
        'category_color': categoryData?['color'],
        'vendor_name': vendorData?['name'],
      });
    } catch (e) {
      _logger.error('Error updating expense: $e');
      rethrow;
    }
  }

  Future<void> deleteExpense(String id) async {
    try {
      final supabase = _supabaseService.client;
      
      await supabase
          .from('expenses')
          .delete()
          .filter('id', 'eq', id);
    } catch (e) {
      _logger.error('Error deleting expense: $e');
      rethrow;
    }
  }

  Future<List<ExpenseModel>> getRecurringExpenses() async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('expenses')
          .select('''
            *,
            expense_categories!inner(name, color),
            vendors(name)
          ''')
          .filter('is_recurring', 'eq', true)
          .order('next_due_date', ascending: true);
      
      return response.map((expense) {
        final categoryData = expense['expense_categories'];
        final vendorData = expense['vendors'];
        
        return ExpenseModel.fromJson({
          ...expense,
          'category_name': categoryData?['name'],
          'category_color': categoryData?['color'],
          'vendor_name': vendorData?['name'],
        });
      }).toList();
    } catch (e) {
      _logger.error('Error fetching recurring expenses: $e');
      rethrow;
    }
  }

  Future<ExpenseModel?> createRecurringExpense(ExpenseModel expense) async {
    try {
      final supabase = _supabaseService.client;
      
      // Validate the expense has the required recurring fields
      if (expense.isRecurring != true || expense.frequency == null) {
        throw Exception('Expense must be recurring and have a frequency');
      }
      
      // Insert the expense
      final response = await supabase.from('expenses').insert({
        'title': expense.title,
        'description': expense.description,
        'amount': expense.amount,
        'date': expense.date.toIso8601String(),
        'category_id': expense.categoryId,
        'property_id': expense.propertyId,
        'room_id': expense.roomId,
        'vendor_id': expense.vendorId,
        'user_id': expense.userId,
        'receipt_number': expense.receiptNumber,
        'is_recurring': true,
        'frequency': expense.frequency?.toString().split('.').last,
        'next_due_date': expense.nextDueDate?.toIso8601String(),
        'occurrences': expense.occurrences,
        'occurrences_completed': 0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).select().single();
      
      return getExpenseById(response['id']);
    } catch (e) {
      _logger.error('Error creating recurring expense: $e');
      return null;
    }
  }

  Future<ExpenseModel?> processRecurringExpense(String id) async {
    try {
      final existingExpense = await getExpenseById(id);
      
      if (existingExpense == null) {
        throw Exception('Expense not found');
      }
      
      if (existingExpense.isRecurring != true || existingExpense.frequency == null) {
        throw Exception('Expense is not recurring');
      }
      
      // Calculate the next due date based on frequency
      DateTime? nextDueDate;
      
      if (existingExpense.nextDueDate != null) {
        switch (existingExpense.frequency) {
          case ExpenseFrequency.weekly:
            nextDueDate = existingExpense.nextDueDate!.add(const Duration(days: 7));
            break;
          case ExpenseFrequency.monthly:
            nextDueDate = DateTime(
              existingExpense.nextDueDate!.year,
              existingExpense.nextDueDate!.month + 1,
              existingExpense.nextDueDate!.day,
            );
            break;
          case ExpenseFrequency.quarterly:
            nextDueDate = DateTime(
              existingExpense.nextDueDate!.year,
              existingExpense.nextDueDate!.month + 3,
              existingExpense.nextDueDate!.day,
            );
            break;
          case ExpenseFrequency.annually:
            nextDueDate = DateTime(
              existingExpense.nextDueDate!.year + 1,
              existingExpense.nextDueDate!.month,
              existingExpense.nextDueDate!.day,
            );
            break;
          default:
            throw Exception('Unsupported frequency: ${existingExpense.frequency}');
        }
      }
      
      // Check if we've reached the maximum occurrences
      final occurrencesCompleted = (existingExpense.occurrencesCompleted ?? 0) + 1;
      final reachedMaxOccurrences = existingExpense.occurrences != null && 
                                   occurrencesCompleted >= existingExpense.occurrences!;
      
      // Update the recurring expense
      await _supabaseService.client.from('expenses').update({
        'occurrences_completed': occurrencesCompleted,
        'next_due_date': reachedMaxOccurrences ? null : nextDueDate?.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', id);
      
      // Create a new expense instance for this occurrence
      await _supabaseService.client.from('expenses').insert({
        'title': existingExpense.title,
        'description': existingExpense.description,
        'amount': existingExpense.amount,
        'date': DateTime.now().toIso8601String(),
        'category_id': existingExpense.categoryId,
        'property_id': existingExpense.propertyId,
        'room_id': existingExpense.roomId,
        'vendor_id': existingExpense.vendorId,
        'user_id': existingExpense.userId,
        'receipt_number': existingExpense.receiptNumber,
        'is_recurring': false,
        'parent_expense_id': id,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      return getExpenseById(id);
    } catch (e) {
      _logger.error('Error processing recurring expense: $e');
      return null;
    }
  }

  Future<Map<String, double>> getCategoryTotals({
    DateTime? startDate,
    DateTime? endDate,
    String? propertyId,
    String? roomId,
  }) async {
    try {
      final expenses = await getExpenses(
        startDate: startDate,
        endDate: endDate,
        propertyId: propertyId,
        roomId: roomId,
        useExpenseSummary: true,
      );
      
      final totals = <String, double>{};
      
      for (final expense in expenses) {
        final categoryName = expense.categoryName ?? 'Uncategorized';
        totals[categoryName] = (totals[categoryName] ?? 0) + expense.amount;
      }
      
      return totals;
    } catch (e) {
      _logger.error('Error calculating category totals: $e');
      return {};
    }
  }

  Future<Map<String, double>> getPropertyTotals({
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
    String? roomId,
  }) async {
    try {
      final expenses = await getExpenses(
        startDate: startDate,
        endDate: endDate,
        categoryId: categoryId,
        roomId: roomId,
        useExpenseSummary: true,
      );
      
      final totals = <String, double>{};
      
      for (final expense in expenses) {
        final propertyName = expense.propertyName ?? 'No Property';
        totals[propertyName] = (totals[propertyName] ?? 0) + expense.amount;
      }
      
      return totals;
    } catch (e) {
      _logger.error('Error calculating property totals: $e');
      return {};
    }
  }
} 