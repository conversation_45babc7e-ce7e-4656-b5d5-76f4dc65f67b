import 'package:flutter/material.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';

class PropertyFormPage extends StatefulWidget {
  final Property? property;

  const PropertyFormPage({super.key, this.property});

  @override
  State<PropertyFormPage> createState() => _PropertyFormPageState();
}

class _PropertyFormPageState extends State<PropertyFormPage> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _imageUrlController = TextEditingController();
  
  // Utility bills
  List<UtilityBill> _utilityBills = [];
  
  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.property != null;
    
    if (_isEditing) {
      _nameController.text = widget.property!.name;
      _addressController.text = widget.property!.address;
      _cityController.text = widget.property!.city;
      _stateController.text = widget.property!.state;
      _zipCodeController.text = widget.property!.zipCode;
      _descriptionController.text = widget.property!.description ?? '';
      _imageUrlController.text = widget.property!.imageUrl ?? '';
      
      // Initialize utility bills
      _utilityBills = List.from(widget.property!.utilityBills);
      
      // Check if Gas utility bill exists, add if missing
      bool hasGasBill = _utilityBills.any((bill) => 
          bill.name.toLowerCase() == 'gas' || 
          bill.name.toLowerCase().contains('gas'));
      
      if (!hasGasBill) {
        _utilityBills.add(UtilityBill(name: 'Gas', rate: 0.0, unit: 'per unit'));
      }
    } else {
      // Add default utility bills for new properties
      _utilityBills = [
        UtilityBill(name: 'Water', rate: 0.0, unit: 'per unit'),
        UtilityBill(name: 'Electricity', rate: 0.0, unit: 'per kWh'),
        UtilityBill(name: 'Gas', rate: 0.0, unit: 'per unit'),
      ];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _descriptionController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Property' : 'Add Property'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Property name
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Property Name',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.home),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a property name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Property address
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'Address',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an address';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // City and State
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: TextFormField(
                            controller: _cityController,
                            decoration: const InputDecoration(
                              labelText: 'City',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a city';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _stateController,
                            decoration: const InputDecoration(
                              labelText: 'State',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a state';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Zip Code
                    TextFormField(
                      controller: _zipCodeController,
                      decoration: const InputDecoration(
                        labelText: 'Zip Code',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.pin),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a zip code';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description (optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    
                    // Image URL
                    TextFormField(
                      controller: _imageUrlController,
                      decoration: const InputDecoration(
                        labelText: 'Image URL (optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.image),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Utility Bills Section
                    Card(
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Utility Bills',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextButton.icon(
                                  onPressed: _addUtilityBill,
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add Custom Bill'),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Configure utility bill rates specific to this property. These rates will be used for automatic bill calculations.',
                              style: TextStyle(color: Colors.grey),
                            ),
                            const SizedBox(height: 16),
                            ..._buildUtilityBillsWidgets(),
                          ],
                        ),
                      ),
                    ),
                    
                    // Submit button
                    ElevatedButton(
                      onPressed: _saveProperty,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        _isEditing ? 'UPDATE PROPERTY' : 'ADD PROPERTY',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  List<Widget> _buildUtilityBillsWidgets() {
    if (_utilityBills.isEmpty) {
      return [
        const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('No utility bills configured'),
          ),
        ),
      ];
    }

    return _utilityBills.asMap().entries.map((entry) {
      final index = entry.key;
      final bill = entry.value;
      
      return Column(
        children: [
          if (index > 0) const Divider(height: 32),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      initialValue: bill.name,
                      decoration: const InputDecoration(
                        labelText: 'Bill Name',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _utilityBills[index].name = value;
                        });
                      },
                    ),
                    const SizedBox(height: 12),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: TextFormField(
                            initialValue: bill.rate.toString(),
                            decoration: const InputDecoration(
                              labelText: 'Rate',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.attach_money),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              setState(() {
                                _utilityBills[index].rate = double.tryParse(value) ?? 0.0;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            initialValue: bill.unit,
                            decoration: const InputDecoration(
                              labelText: 'Unit',
                              border: OutlineInputBorder(),
                              hintText: 'e.g., per kWh',
                            ),
                            onChanged: (value) {
                              setState(() {
                                _utilityBills[index].unit = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      initialValue: bill.notes,
                      decoration: const InputDecoration(
                        labelText: 'Notes (optional)',
                        border: OutlineInputBorder(),
                        hintText: 'Additional information about this bill',
                      ),
                      onChanged: (value) {
                        setState(() {
                          _utilityBills[index].notes = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                color: Colors.red,
                onPressed: () {
                  setState(() {
                    _utilityBills.removeAt(index);
                  });
                },
              ),
            ],
          ),
        ],
      );
    }).toList();
  }

  void _addUtilityBill() {
    showDialog(
      context: context,
      builder: (context) {
        final nameController = TextEditingController();
        final rateController = TextEditingController(text: '0.0');
        final unitController = TextEditingController();
        
        return AlertDialog(
          title: const Text('Add Utility Bill'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Bill Name',
                    hintText: 'e.g., Gas, Internet, Cable TV, Security',
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: rateController,
                  decoration: const InputDecoration(
                    labelText: 'Rate',
                    prefixIcon: Icon(Icons.attach_money),
                    hintText: 'Cost per unit of measurement',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: unitController,
                  decoration: const InputDecoration(
                    labelText: 'Unit (optional)',
                    hintText: 'e.g., per month, per unit, per kWh, per cubic meter, per therm',
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (nameController.text.isNotEmpty) {
                  setState(() {
                    _utilityBills.add(
                      UtilityBill(
                        name: nameController.text,
                        rate: double.tryParse(rateController.text) ?? 0.0,
                        unit: unitController.text.isNotEmpty ? unitController.text : null,
                      ),
                    );
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _saveProperty() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      if (_isEditing) {
        // Update existing property
        final updatedProperty = await serviceLocator.propertyService.updateProperty(
          id: widget.property!.id,
          name: _nameController.text,
          address: _addressController.text,
          city: _cityController.text,
          state: _stateController.text,
          zipCode: _zipCodeController.text,
          description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
          imageUrl: _imageUrlController.text.isNotEmpty ? _imageUrlController.text : null,
          utilityBills: _utilityBills,
        );
        
        if (mounted) {
          if (updatedProperty != null) {
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text('Property "${_nameController.text}" updated successfully'),
                    ),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.pop(context, true); // Return true to indicate success
          } else {
            // Show error if property couldn't be updated
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.error, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text('Failed to update property. The property may have been deleted.'),
                    ),
                  ],
                ),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 5),
                behavior: SnackBarBehavior.floating,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        }
      } else {
        // Add new property
        await serviceLocator.propertyService.addProperty(
          name: _nameController.text,
          address: _addressController.text,
          city: _cityController.text,
          state: _stateController.text,
          zipCode: _zipCodeController.text,
          description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
          imageUrl: _imageUrlController.text.isNotEmpty ? _imageUrlController.text : null,
          utilityBills: _utilityBills,
        );
        
        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('Property "${_nameController.text}" added successfully'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate success
        }
      }
    } catch (e) {
      if (mounted) {
        // Show detailed error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(_isEditing ? 'Failed to update property' : 'Failed to add property'),
                      Text(
                        e.toString(),
                        style: const TextStyle(fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'DISMISS',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    } finally {
      // Only set state if not already set in the catch block
      if (mounted && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 