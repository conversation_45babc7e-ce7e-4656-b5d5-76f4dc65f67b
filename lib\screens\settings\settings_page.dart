import 'package:currency_picker/currency_picker.dart';
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../services/service_locator.dart';
import '../legal/terms_and_conditions_screen.dart';
import '../legal/privacy_policy_screen.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> with SingleTickerProviderStateMixin {
  late Currency _selectedCurrency;
  late Locale _selectedLocale;
  bool _isSyncing = false;
  late AnimationController _syncAnimationController;
  final List<Map<String, String>> _supportedLocales = [
    {'code': 'en', 'name': 'English'},
    {'code': 'sw', 'name': 'Kiswahili'},
  ];

  @override
  void initState() {
    super.initState();
    _selectedCurrency = serviceLocator.settingsService.selectedCurrency;
    _selectedLocale = serviceLocator.settingsService.currentLocale;
    _syncAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    // Start continuous rotation
    _syncAnimationController.repeat();
  }

  @override
  void dispose() {
    _syncAnimationController.dispose();
    super.dispose();
  }

  String _getLocaleName(Locale locale) {
    for (var supportedLocale in _supportedLocales) {
      if (supportedLocale['code'] == locale.languageCode) {
        return supportedLocale['name']!;
      }
    }
    return 'Unknown';
  }

  // Sync settings across devices
  Future<void> _syncSettings() async {
    if (!serviceLocator.authService.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please log in to sync settings'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSyncing = true;
    });
    // No need to start animation as it's already continuously rotating

    try {
      final success = await serviceLocator.settingsService.saveSettingsToDatabase();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success 
              ? 'Settings synced successfully' 
              : 'Failed to sync settings'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error syncing settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
        // No need to stop animation as we want it to continuously rotate
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations?.translate('settings') ?? 'Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Language settings
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    'Language',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: const Text('App Language'),
                  subtitle: Text(_getLocaleName(_selectedLocale)),
                  leading: const Icon(Icons.language),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: _showLanguagePicker,
                ),
              ],
            ),
          ),

          // Currency setting
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    localizations?.translate('currency') ?? 'Currency',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: Text(
                    localizations?.translate('defaultCurrency') ??
                        'Default Currency',
                  ),
                  subtitle: Text(
                    '${_selectedCurrency.name} (${_selectedCurrency.code}) ${_selectedCurrency.symbol}',
                  ),
                  leading: const Icon(Icons.currency_exchange),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    _showCurrencyPicker();
                  },
                ),
                // Currency example
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations?.translate('currencyExample') ??
                            'Example:',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        serviceLocator.settingsService.formatCurrency(1234.56),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Sync notice
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: const Text(
                      'User settings will be synced across all devices',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: _isSyncing ? null : _syncSettings,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: RotationTransition(
                        turns: _syncAnimationController,
                        child: Icon(
                          Icons.sync,
                          color: _isSyncing ? Theme.of(context).primaryColor : Colors.grey,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Legal Documents
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    'Legal',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: const Text('Terms & Conditions'),
                  subtitle: const Text('View our terms of service'),
                  leading: Icon(
                    Icons.description,
                    color: Colors.blue.shade600,
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const TermsAndConditionsScreen(),
                      ),
                    );
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Privacy Policy'),
                  subtitle: const Text('Learn how we protect your data'),
                  leading: Icon(
                    Icons.privacy_tip,
                    color: Colors.green.shade600,
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // App Information
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    'About',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: const Text('Tenanta'),
                  subtitle: const Text('Property Management App\nVersion 1.0.0\nDeveloped by Creative DesignersKE'),
                  leading: Icon(
                    Icons.info_outline,
                    color: Colors.orange.shade600,
                  ),
                  isThreeLine: true,
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Contact Support'),
                  subtitle: const Text('<EMAIL>'),
                  leading: Icon(
                    Icons.support_agent,
                    color: Colors.purple.shade600,
                  ),
                  trailing: const Icon(Icons.email, size: 16),
                  onTap: () {
                    // You can implement email launching here if needed
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Contact us at: <EMAIL>'),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguagePicker() {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                _supportedLocales.map((locale) {
                  return ListTile(
                    title: Text(locale['name']!),
                    onTap: () async {
                      final newLocale = Locale(locale['code']!);

                      // Close dialog before async operation
                      Navigator.pop(dialogContext);

                      // Update locale in settings service
                      await serviceLocator.settingsService.setLocale(newLocale);
                      
                      // Update UI
                      if (mounted) {
                        setState(() {
                          _selectedLocale = newLocale;
                        });
                        
                        // Rebuild the entire app to apply language change
                        _restartApp();
                      }
                    },
                  );
                }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(dialogContext);
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  // Method to restart app to apply language change
  void _restartApp() {
    // Need to restart the entire app to apply language changes
    // This is because the MaterialApp needs to rebuild with the new locale
    final context = this.context;
    
    // Navigate to dashboard and clear all previous routes
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/dashboard', 
      (route) => false,
    );
  }

  void _showCurrencyPicker() {
    showCurrencyPicker(
      context: context,
      showFlag: true,
      showCurrencyName: true,
      showCurrencyCode: true,
      onSelect: (Currency currency) async {
        // Update currency in settings service
        await serviceLocator.settingsService.setCurrency(currency);
        
        // Update UI
        if (mounted) {
          setState(() {
            _selectedCurrency = currency;
          });
        }
      },
    );
  }
}
