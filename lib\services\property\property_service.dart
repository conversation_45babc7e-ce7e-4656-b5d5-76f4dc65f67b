import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/property/property_model.dart';
import '../../utils/logger.dart';
import '../service_locator.dart';

/// Service for managing properties using Supabase
class PropertyService {
  // Stream controller for property changes
  final _propertyStreamController = StreamController<List<Property>>.broadcast();
  
  // Singleton instance
  static final PropertyService _instance = PropertyService._internal();
  
  // Supabase client
  late final SupabaseClient _supabase;
  
  // Factory constructor
  factory PropertyService() {
    return _instance;
  }
  
  // Private constructor
  PropertyService._internal() {
    _supabase = Supabase.instance.client;
    _refreshProperties();
  }
  
  /// Get stream of property updates
  Stream<List<Property>> get propertiesStream => _propertyStreamController.stream;
  
  /// Fetch all properties for the current user
  Future<List<Property>> _fetchAllProperties() async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        final response = await _supabase.from('properties').select('''
          *,
          utility_bills(*)
        ''').order('created_at');
        
        final List<dynamic> data = response;
        return data.map((propertyData) {
          // Extract utility bills from the joined data
          final List<dynamic> utilityBillsData = propertyData['utility_bills'] ?? [];
          final utilityBills = utilityBillsData.map((billData) => 
            UtilityBill(
              name: billData['name'],
              rate: (billData['rate'] as num).toDouble(),
              unit: billData['unit'],
              notes: billData['notes'],
            )
          ).toList();
          
          // Create property with the utility bills
          return Property(
            id: propertyData['id'],
            name: propertyData['name'],
            address: propertyData['address'],
            city: propertyData['city'],
            state: propertyData['state'],
            zipCode: propertyData['zip_code'],
            description: propertyData['description'],
            imageUrl: propertyData['image_url'],
            utilityBills: utilityBills,
            additionalInfo: propertyData['additional_info'],
            createdAt: DateTime.parse(propertyData['created_at']),
            updatedAt: DateTime.parse(propertyData['updated_at']),
          );
        }).toList();
      } catch (error) {
        AppLogger.error('Error fetching properties', error);
        return [];
      }
    });
  }
  
  /// Manually refresh properties data
  Future<void> _refreshProperties() async {
    final properties = await _fetchAllProperties();
    if (!_propertyStreamController.isClosed) {
      _propertyStreamController.add(properties);
    }
  }
  
  /// Get all properties
  Future<List<Property>> getAllProperties() async {
    final properties = await _fetchAllProperties();
    
    // Update the stream with fresh data
    if (!_propertyStreamController.isClosed) {
      _propertyStreamController.add(properties);
    }
    
    return properties;
  }
  
  /// Get a property by ID
  Future<Property?> getPropertyById(String id) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Fetch property with utility bills using join
        final response = await _supabase
            .from('properties')
            .select('''
              *,
              utility_bills(*)
            ''')
            .eq('id', id)
            .single();
        
        // Extract utility bills from the response
        final List<dynamic> utilityBillsData = response['utility_bills'] ?? [];
        final utilityBills = utilityBillsData.map((billData) => 
          UtilityBill.fromJson(billData)
        ).toList();
        
        // Create property with the extracted utility bills
        final property = Property.fromJson(response);
        
        // Set utility bills separately because fromJson might not handle them correctly
        return property.copyWith(utilityBills: utilityBills);
      } catch (error) {
        AppLogger.error('Error fetching property by ID: $error');
        return null;
      }
    });
  }
  
  /// Add a new property
  Future<Property> addProperty({
    required String name,
    required String address,
    required String city,
    required String state,
    required String zipCode,
    String? description,
    String? imageUrl,
    List<UtilityBill>? utilityBills,
    Map<String, dynamic>? additionalInfo,
  }) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Get the current user's ID
        final userId = _supabase.auth.currentUser?.id;
        if (userId == null) {
          throw Exception('User is not authenticated');
        }

        // Insert the property
        final response = await _supabase.from('properties').insert({
          'name': name,
          'address': address,
          'city': city,
          'state': state,
          'zip_code': zipCode,
          'description': description,
          'image_url': imageUrl,
          'additional_info': additionalInfo,
          'user_id': userId, // Set the user_id field
        }).select().single();
        
        final propertyId = response['id'];
        
        // Add utility bills if provided
        if (utilityBills != null && utilityBills.isNotEmpty) {
          final billsData = utilityBills.map((bill) => {
            'property_id': propertyId,
            'name': bill.name,
            'rate': bill.rate,
            'unit': bill.unit,
            'notes': bill.notes,
          }).toList();
          
          await _supabase.from('utility_bills').insert(billsData);
        }
        
        // Refresh the properties stream
        _refreshProperties();
        
        // Return the newly created property with utility bills
        return await getPropertyById(propertyId) ?? Property(
          id: propertyId,
          name: name,
          address: address,
          city: city,
          state: state,
          zipCode: zipCode,
          description: description,
          imageUrl: imageUrl,
          utilityBills: utilityBills ?? [],
          additionalInfo: additionalInfo,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } catch (error) {
        AppLogger.error('Error adding property', error);
        throw Exception('Failed to add property: $error');
      }
    });
  }
  
  /// Update an existing property
  Future<Property?> updateProperty({
    required String id,
    String? name,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? description,
    String? imageUrl,
    List<UtilityBill>? utilityBills,
    Map<String, dynamic>? additionalInfo,
  }) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Get current property to determine changes
        final currentProperty = await getPropertyById(id);
        if (currentProperty == null) {
          return null;
        }
        
        // Update property data
        final updateData = <String, dynamic>{};
        if (name != null) updateData['name'] = name;
        if (address != null) updateData['address'] = address;
        if (city != null) updateData['city'] = city;
        if (state != null) updateData['state'] = state;
        if (zipCode != null) updateData['zip_code'] = zipCode;
        if (description != null) updateData['description'] = description;
        if (imageUrl != null) updateData['image_url'] = imageUrl;
        if (additionalInfo != null) updateData['additional_info'] = additionalInfo;
        
        if (updateData.isNotEmpty) {
          await _supabase.from('properties').update(updateData).eq('id', id);
        }
        
        // Update utility bills if provided
        if (utilityBills != null) {
          // Delete existing utility bills
          await _supabase.from('utility_bills').delete().eq('property_id', id);
          
          // Insert new utility bills
          if (utilityBills.isNotEmpty) {
            final billsData = utilityBills.map((bill) => {
              'property_id': id,
              'name': bill.name,
              'rate': bill.rate,
              'unit': bill.unit,
              'notes': bill.notes,
            }).toList();
            
            await _supabase.from('utility_bills').insert(billsData);
          }
        }
        
        // Refresh the properties stream
        _refreshProperties();
        
        // Return the updated property
        return await getPropertyById(id);
      } catch (error) {
        AppLogger.error('Error updating property', error);
        return null;
      }
    });
  }
  
  /// Delete a property
  Future<bool> deleteProperty(String id) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // The utility bills will be automatically deleted due to the CASCADE constraint
        await _supabase.from('properties').delete().eq('id', id);
        return true;
      } catch (error) {
        AppLogger.error('Error deleting property', error);
        return false;
      }
    });
  }
  
  /// Add or update a utility bill for a property
  Future<bool> updateUtilityBill(String propertyId, UtilityBill bill, {String? billId}) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        if (billId != null) {
          // Update existing bill
          await _supabase.from('utility_bills').update({
            'name': bill.name,
            'rate': bill.rate,
            'unit': bill.unit,
            'notes': bill.notes,
          }).eq('id', billId);
        } else {
          // Add new bill
          await _supabase.from('utility_bills').insert({
            'property_id': propertyId,
            'name': bill.name,
            'rate': bill.rate,
            'unit': bill.unit,
            'notes': bill.notes,
          });
        }
        return true;
      } catch (error) {
        AppLogger.error('Error updating utility bill', error);
        return false;
      }
    });
  }
  
  /// Remove a utility bill
  Future<bool> removeUtilityBill(String billId) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        await _supabase.from('utility_bills').delete().eq('id', billId);
        return true;
      } catch (error) {
        AppLogger.error('Error removing utility bill', error);
        return false;
      }
    });
  }
  
  /// Dispose of resources
  void dispose() {
    _propertyStreamController.close();
  }
} 