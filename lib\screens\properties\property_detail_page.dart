import 'package:flutter/material.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../rooms/room_detail_page.dart';
import '../rooms/room_form_page.dart';
import 'property_form_page.dart';

class PropertyDetailPage extends StatefulWidget {
  final String propertyId;

  const PropertyDetailPage({super.key, required this.propertyId});

  @override
  State<PropertyDetailPage> createState() => _PropertyDetailPageState();
}

class _PropertyDetailPageState extends State<PropertyDetailPage> {
  Property? _property;
  List<Room> _rooms = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPropertyData();
  }

  Future<void> _loadPropertyData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get property from service
      final property = await serviceLocator.propertyService.getPropertyById(
        widget.propertyId,
      );

      if (property != null) {
        // Get rooms for this property
        final rooms = await serviceLocator.roomService.getRoomsByPropertyId(
          widget.propertyId,
        );

        if (mounted) {
          setState(() {
            _property = property;
            _rooms = rooms;
            _isLoading = false;
          });
        }
      } else {
        // Property not found, go back
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Property not found'),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Error loading property data: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Property Details'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_property!.name),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit Property',
            onPressed: _editProperty,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'Delete Property',
            onPressed: _deleteProperty,
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 8),
        children: [
          // Property header with image
          _buildPropertyHeader(),

          // Utility Bills section
          if (_property!.utilityBills.isNotEmpty) _buildUtilityBillsSection(),

          // Rooms section
          _buildRoomsSection(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addRoom,
        icon: const Icon(Icons.add),
        label: const Text('ADD ROOM'),
        tooltip: 'Add a new room',
      ),
    );
  }

  Widget _buildPropertyHeader() {
    return Card(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property header with image
            Row(
              children: [
                // Property image (profile-like)
                Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/images/Apartment.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Property name and type
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _property!.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _property!.city,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(height: 1),
            const SizedBox(height: 16),

            // Address row - compact
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.deepPurple.withValues(alpha: 30),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.location_on,
                    size: 18,
                    color: Colors.deepPurple,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Address',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_property!.address}, ${_property!.city}, ${_property!.state} ${_property!.zipCode}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Room occupancy statistics
            const SizedBox(height: 16),
            _buildRoomStatistics(),

            // Description if available
            if (_property!.description != null &&
                _property!.description!.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 12),
              Text(
                'Description',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _property!.description!,
                style: const TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Room statistics widget
  Widget _buildRoomStatistics() {
    // Calculate room statistics
    final totalRooms = _rooms.length;
    final vacantRooms =
        _rooms
            .where((r) => r.occupancyStatus == RoomOccupancyStatus.vacant)
            .length;
    final occupiedRooms =
        _rooms
            .where((r) => r.occupancyStatus == RoomOccupancyStatus.occupied)
            .length;
    final reservedRooms =
        _rooms
            .where((r) => r.occupancyStatus == RoomOccupancyStatus.reserved)
            .length;
    final maintenanceRooms =
        _rooms
            .where((r) => r.occupancyStatus == RoomOccupancyStatus.maintenance)
            .length;
            
    // Calculate total monthly income
    double totalMonthlyIncome = 0;
    for (final room in _rooms) {
      totalMonthlyIncome += room.rentalPrice;
    }
    final formattedIncome = CurrencyFormatter.formatAmountWithCode(totalMonthlyIncome);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Room Statistics',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            // Total Monthly Income
            Row(
              children: [
                const Icon(Icons.attach_money, size: 14, color: Colors.green),
                const SizedBox(width: 4),
                Text(
                  formattedIncome,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total',
                totalRooms.toString(),
                Icons.meeting_room,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'Vacant',
                vacantRooms.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'Occupied',
                occupiedRooms.toString(),
                Icons.person,
                Colors.orange,
              ),
            ),
          ],
        ),
        if (reservedRooms > 0 || maintenanceRooms > 0) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              if (reservedRooms > 0)
                Expanded(
                  child: _buildStatCard(
                    'Reserved',
                    reservedRooms.toString(),
                    Icons.event_available,
                    Colors.amber,
                  ),
                ),
              if (reservedRooms > 0 && maintenanceRooms > 0)
                const SizedBox(width: 8),
              if (maintenanceRooms > 0)
                Expanded(
                  child: _buildStatCard(
                    'Maintenance',
                    maintenanceRooms.toString(),
                    Icons.build,
                    Colors.red,
                  ),
                ),
              // Add empty expanded widgets to maintain layout if needed
              if (reservedRooms > 0 && maintenanceRooms == 0)
                Expanded(child: Container()),
              if (reservedRooms == 0 && maintenanceRooms > 0)
                Expanded(child: Container()),
            ],
          ),
        ],
      ],
    );
  }

  // Stat card widget
  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 76)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 18, color: color),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 204)),
          ),
        ],
      ),
    );
  }

  Widget _buildUtilityBillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Utility Bills section header with background color
        Container(
          margin: const EdgeInsets.fromLTRB(16, 8, 16, 12),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.amber.withValues(alpha: 30),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.amber.withValues(alpha: 50), width: 1),
          ),
          child: Row(
            children: [
              const Icon(Icons.receipt_long, size: 20, color: Colors.amber),
              const SizedBox(width: 8),
              const Text(
                'Utility Bills',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              if (_property!.utilityBills.isNotEmpty)
                Text(
                  '${_property!.utilityBills.length} ${_property!.utilityBills.length == 1 ? 'bill' : 'bills'}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
            ],
          ),
        ),

        // Utility bills cards
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _property!.utilityBills.length,
          itemBuilder: (context, index) {
            final bill = _property!.utilityBills[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getUtilityBillColor(bill.name).withValues(alpha: 30),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getUtilityBillIcon(bill.name),
                    color: _getUtilityBillColor(bill.name),
                    size: 20,
                  ),
                ),
                title: Text(
                  bill.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      '${bill.formattedRate} ${bill.unit ?? 'per unit'}',
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    if (bill.notes != null && bill.notes!.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Text(
                        bill.notes!,
                        style: const TextStyle(
                          color: Colors.black54,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Color _getUtilityBillColor(String billName) {
    final name = billName.toLowerCase();
    if (name.contains('water')) {
      return Colors.blue;
    }
    if (name.contains('electric')) {
      return Colors.amber;
    }
    if (name.contains('gas')) {
      return Colors.orange;
    }
    if (name.contains('internet') || name.contains('wifi')) {
      return Colors.indigo;
    }
    if (name.contains('trash') || name.contains('garbage')) {
      return Colors.green;
    }
    if (name.contains('cable') || name.contains('tv')) {
      return Colors.red;
    }
    return Colors.purple;
  }

  IconData _getUtilityBillIcon(String billName) {
    final name = billName.toLowerCase();
    if (name.contains('water')) {
      return Icons.water_drop;
    }
    if (name.contains('electric')) {
      return Icons.electric_bolt;
    }
    if (name.contains('gas')) {
      return Icons.local_fire_department;
    }
    if (name.contains('internet') || name.contains('wifi')) {
      return Icons.wifi;
    }
    if (name.contains('trash') || name.contains('garbage')) {
      return Icons.delete;
    }
    if (name.contains('cable') || name.contains('tv')) {
      return Icons.tv;
    }
    if (name.contains('security')) {
      return Icons.security;
    }
    if (name.contains('clean')) {
      return Icons.cleaning_services;
    }
    return Icons.receipt;
  }

  Widget _buildRoomsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Rooms section header with background color
        Container(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 12),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.deepPurple.withValues(alpha: 30),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.deepPurple.withValues(alpha: 50),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.meeting_room,
                size: 20,
                color: Colors.deepPurple,
              ),
              const SizedBox(width: 8),
              const Text(
                'Rooms',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),

        // Rooms list or empty state
        _rooms.isEmpty
            ? _buildEmptyRoomsState()
            : ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _rooms.length,
              itemBuilder: (context, index) {
                final room = _rooms[index];
                return _buildRoomCard(room);
              },
            ),

        // Add some space at the bottom for the FAB
        const SizedBox(height: 80),
      ],
    );
  }

  Widget _buildEmptyRoomsState() {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      elevation: 0,
      color: Colors.grey.shade50,
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_home, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            const Text(
              'No rooms yet',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap the + button to add your first room',
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoomCard(Room room) {
    // Get color based on occupancy status
    Color statusColor;
    String statusText;
    switch (room.occupancyStatus) {
      case RoomOccupancyStatus.vacant:
        statusColor = Colors.green;
        statusText = "VACANT";
        break;
      case RoomOccupancyStatus.occupied:
        statusColor = Colors.blue;
        statusText = "OCCUPIED";
        break;
      case RoomOccupancyStatus.reserved:
        statusColor = Colors.orange;
        statusText = "RESERVED";
        break;
      case RoomOccupancyStatus.maintenance:
        statusColor = Colors.red;
        statusText = "MAINTENANCE";
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _viewRoomDetails(room),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Room header with name and status
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 30),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Room name and icon
                  Row(
                    children: [
                      Icon(Icons.meeting_room, color: statusColor, size: 18),
                      const SizedBox(width: 8),
                      Text(
                        room.name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),

                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 50),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: statusColor),
                    ),
                    child: Text(
                      statusText,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 11,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Room details - more compact
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // First row: Price and furnished status side by side
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Rental price
                      Expanded(
                        child: _buildRoomInfoItem(
                          icon: Icons.attach_money,
                          iconColor: Colors.blue.shade700,
                          backgroundColor: Colors.blue.shade50,
                          title: 'Rental Price',
                          value: CurrencyFormatter.formatAmount(room.rentalPrice),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Furnished status
                      Expanded(
                        child: _buildRoomInfoItem(
                          icon:
                              room.isFurnished
                                  ? Icons.check_circle
                                  : Icons.cancel,
                          iconColor:
                              room.isFurnished ? Colors.green : Colors.red,
                          backgroundColor:
                              room.isFurnished
                                  ? Colors.green.shade50
                                  : Colors.red.shade50,
                          title: 'Furnished',
                          value: room.isFurnished ? 'Yes' : 'No',
                        ),
                      ),
                    ],
                  ),

                  // Room type
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 12),
                      _buildRoomInfoItem(
                        icon: Icons.category,
                        iconColor: Colors.amber.shade700,
                        backgroundColor: Colors.amber.shade50,
                        title: 'Room Type',
                        value: _getRoomTypeName(room),
                      ),
                    ],
                  ),

                  // Room size if available
                  if (room.size != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 12),
                        _buildRoomInfoItem(
                          icon: Icons.square_foot,
                          iconColor: Colors.purple,
                          backgroundColor: Colors.purple.shade50,
                          title: 'Size',
                          value: '${room.size!.toStringAsFixed(0)} sq ft',
                        ),
                      ],
                    ),

                  // Amenities with improved visibility
                  if (room.amenities.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: const Text(
                            'Amenities',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Amenities in a wrap layout with better visibility
                        Wrap(
                          spacing: 6,
                          runSpacing: 6,
                          children:
                              room.amenities.map((amenity) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.lightBlue.shade50,
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.lightBlue.shade200,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _getAmenityIcon(amenity.displayName),
                                        size: 12,
                                        color: Colors.blue.shade700,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        amenity.displayName,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.blue.shade900,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // View button - align to the right
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 0, 8, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _viewRoomDetails(room),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('VIEW'),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method for room info items with circular background
  Widget _buildRoomInfoItem({
    required IconData icon,
    required Color iconColor,
    required Color backgroundColor,
    required String title,
    required String value,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: backgroundColor,
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 14, color: iconColor),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to get amenity icons
  IconData _getAmenityIcon(String amenityName) {
    final name = amenityName.toLowerCase();
    if (name.contains('air') || name.contains('conditioning')) {
      return Icons.ac_unit;
    }
    if (name.contains('bathroom') || name.contains('toilet')) {
      return Icons.bathroom;
    }
    if (name.contains('kitchen')) {
      return Icons.kitchen;
    }
    if (name.contains('tv') || name.contains('television')) {
      return Icons.tv;
    }
    if (name.contains('wifi') || name.contains('internet')) {
      return Icons.wifi;
    }
    if (name.contains('laundry') || name.contains('washer')) {
      return Icons.local_laundry_service;
    }
    if (name.contains('parking')) {
      return Icons.local_parking;
    }
    return Icons.check_circle;
  }

  void _addRoom() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => RoomFormPage(propertyId: widget.propertyId),
      ),
    );

    if (result == true) {
      _loadPropertyData();
    }
  }

  void _viewRoomDetails(Room room) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => RoomDetailPage(roomId: room.id)),
    ).then((_) => _loadPropertyData());
  }

  void _editProperty() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => PropertyFormPage(property: _property),
      ),
    );

    if (result == true) {
      _loadPropertyData();
    }
  }

  void _deleteProperty() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Property'),
            content: Text(
              'Are you sure you want to delete "${_property!.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  // Capture navigator and scaffold messenger before async gap
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  setState(() {
                    _isLoading = true;
                  });

                  try {
                    final success = await serviceLocator.propertyService
                        .deleteProperty(widget.propertyId);

                    if (mounted) {
                      if (success) {
                        navigator.pop();
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                const Icon(
                                  Icons.check_circle,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Property "${_property!.name}" deleted successfully',
                                ),
                              ],
                            ),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      } else {
                        setState(() {
                          _isLoading = false;
                        });
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(
                            backgroundColor: Colors.red,
                            behavior: SnackBarBehavior.floating,
                            content: Row(
                              children: [
                                Icon(Icons.error_outline, color: Colors.white),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Text('Failed to delete property'),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                          content: Row(
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 8),
                              Expanded(child: Text('Error: ${e.toString()}')),
                            ],
                          ),
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('DELETE'),
              ),
            ],
          ),
    );
  }

  // Helper method to get room type display name
  String _getRoomTypeName(Room room) {
    if (room.roomTypeId != null || room.customRoomType != null) {
      return room.customRoomType ?? 'Standard Room';
    }
    return 'Standard Room';
  }
}
