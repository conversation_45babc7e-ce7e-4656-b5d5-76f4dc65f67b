import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/expense/expense_category_model.dart';
import '../../models/expense/expense_model.dart';
import '../../models/property/property_model.dart' as property_model;
import '../../models/room/room_model.dart' as room_model;
import '../../services/expense/expense_category_service.dart';
import '../../services/expense/expense_service.dart';
import '../../services/property/property_service.dart';
import '../../services/room/room_service.dart';
import '../../services/service_locator.dart';
import '../../services/supabase_service.dart';
import '../../utils/form_validators.dart';
import '../../widgets/app_loading_indicator.dart';

class ExpenseFormPage extends StatefulWidget {
  final String? expenseId;

  const ExpenseFormPage({super.key, this.expenseId});

  @override
  State<ExpenseFormPage> createState() => _ExpenseFormPageState();
}

class _ExpenseFormPageState extends State<ExpenseFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _occurrencesController = TextEditingController();
  final _vendorNameController = TextEditingController();
  final _receiptNumberController = TextEditingController();

  final ExpenseService _expenseService = serviceLocator.expenseService;
  final ExpenseCategoryService _categoryService = serviceLocator.expenseCategoryService;
  final PropertyService _propertyService = serviceLocator.propertyService;
  final RoomService _roomService = serviceLocator.roomService;
  final SupabaseService _supabaseService = serviceLocator.supabaseService;

  bool _isLoading = false;
  bool _isRecurring = false;
  DateTime _selectedDate = DateTime.now();
  DateTime? _selectedEndDate;
  ExpenseFrequency _frequency = ExpenseFrequency.monthly;

  ExpenseCategoryModel? _selectedCategory;
  property_model.Property? _selectedProperty;
  room_model.Room? _selectedRoom;

  List<ExpenseCategoryModel> _categories = [];
  List<property_model.Property> _properties = [];
  List<room_model.Room> _rooms = [];

  bool _isEdit = false;
  ExpenseModel? _expense;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load categories, vendors, and properties
      final categoriesFuture = _categoryService.getCategories(includeDefault: true);
      final propertiesFuture = _propertyService.getAllProperties();

      final results = await Future.wait([
        categoriesFuture,
        propertiesFuture,
      ]);

      if (!mounted) return;

      setState(() {
        _categories = results[0] as List<ExpenseCategoryModel>;
        _properties = results[1] as List<property_model.Property>;
        
        // Set default category if available and not editing
        if (_categories.isNotEmpty && !_isEdit && _selectedCategory == null) {
          _selectedCategory = _categories.first;
        }
      });

      // If editing an existing expense
      if (widget.expenseId != null) {
        _isEdit = true;
        _expense = await _expenseService.getExpenseById(widget.expenseId!);
        
        if (!mounted) return;
        
        _populateFormFields();
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _populateFormFields() {
    if (_expense != null) {
      _titleController.text = _expense!.title;
      _descriptionController.text = _expense!.description;
      _amountController.text = _expense!.amount.toString();
      _selectedDate = _expense!.date;
      _isRecurring = _expense!.isRecurring;
      
      // Set vendor name if available
      if (_expense!.vendorName != null) {
        _vendorNameController.text = _expense!.vendorName!;
      }
      
      // Set receipt number if available
      if (_expense!.receiptNumber != null) {
        _receiptNumberController.text = _expense!.receiptNumber!;
      }
      
      if (_isRecurring) {
        _frequency = _expense!.frequency ?? ExpenseFrequency.monthly;
        _selectedEndDate = _expense!.endDate;
        if (_expense!.occurrences != null) {
          _occurrencesController.text = _expense!.occurrences.toString();
        }
      }
      
      // Set selected category
      if (_expense!.categoryId != null) {
        for (var category in _categories) {
          if (category.id == _expense!.categoryId) {
            _selectedCategory = category;
            break;
          }
        }
      }
      
      // Set selected property
      if (_expense!.propertyId != null) {
        for (var property in _properties) {
          if (property.id == _expense!.propertyId) {
            _selectedProperty = property;
            break;
          }
        }
        
        // Load rooms for the selected property
        if (_selectedProperty != null) {
          _loadRooms(_selectedProperty!.id);
        }
      }
    }
  }

  Future<void> _loadRooms(String propertyId) async {
    try {
      final rooms = await _roomService.getRoomsByPropertyId(propertyId);
      setState(() {
        _rooms = rooms;
        
        // Set selected room if editing and room belongs to this property
        if (_isEdit && _expense!.roomId != null) {
          for (var room in _rooms) {
            if (room.id == _expense!.roomId) {
              _selectedRoom = room;
              break;
            }
          }
        } else {
          _selectedRoom = null;
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading rooms: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String? _validateTitle(String? value) {
    final required = FormValidators.required(value);
    if (required != null) return required;
    
    final minLength = FormValidators.minLength(value, 3);
    if (minLength != null) return minLength;
    
    return null;
  }

  String? _validateVendorName(String? value) {
    // Vendor name is optional
    if (value == null || value.isEmpty) return null;
    
    // If provided, ensure it's at least 2 characters
    if (value.length < 2) {
      return 'Vendor name must be at least 2 characters';
    }
    
    return null;
  }

  String? _validateReceiptNumber(String? value) {
    // Receipt number is optional
    if (value == null || value.isEmpty) return null;
    
    // If provided, ensure it follows a valid format (alphanumeric)
    if (!RegExp(r'^[a-zA-Z0-9-]+$').hasMatch(value)) {
      return 'Receipt number can only contain letters, numbers, and hyphens';
    }
    
    return null;
  }

  String? _validateAmount(String? value) {
    final nonZero = FormValidators.nonZeroAmount(value);
    if (nonZero != null) return nonZero;
    
    try {
      double.parse(value!);
      return null;
    } catch (e) {
      return 'Please enter a valid number';
    }
  }

  String? _validateOccurrences(String? value) {
    if (value == null || value.isEmpty) return null; // Optional field
    
    try {
      final occurrences = int.parse(value);
      if (occurrences <= 0) {
        return 'Number of occurrences must be positive';
      }
      return null;
    } catch (e) {
      return 'Please enter a valid number';
    }
  }

  bool _validateDates() {
    if (!_isRecurring) return true;
    
    if (_selectedEndDate != null) {
      if (_selectedEndDate!.isBefore(_selectedDate)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('End date must be after start date'),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    }
    return true;
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate() || !_validateDates()) {
      return;
    }

    if (_selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a category'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      final currentUserId = _supabaseService.client.auth.currentUser!.id;
      
      // Create expense data directly without using the model's toJson
      final expenseData = {
        'title': _titleController.text,
        'description': _descriptionController.text,
        'amount': amount,
        'date': _selectedDate.toIso8601String(),
        'category_id': _selectedCategory?.id,
        'property_id': _selectedProperty?.id,
        'room_id': _selectedRoom?.id,
        'vendor_name': _vendorNameController.text.isNotEmpty ? _vendorNameController.text : null,
        'receipt_number': _receiptNumberController.text.isNotEmpty ? _receiptNumberController.text : null,
        'is_recurring': _isRecurring,
        'user_id': currentUserId,
        'created_at': DateTime.now().toIso8601String(),
      };
      
      // Add recurring-specific fields if needed
      if (_isRecurring) {
        expenseData['frequency'] = _frequency.toString().split('.').last;
        expenseData['next_due_date'] = _calculateNextDueDate().toIso8601String();
        
        if (_selectedEndDate != null) {
          expenseData['end_date'] = _selectedEndDate!.toIso8601String();
        }
        
        if (_occurrencesController.text.isNotEmpty) {
          expenseData['occurrences'] = int.parse(_occurrencesController.text);
        }
      }
      
      // If editing, add the ID and update
      if (_isEdit && _expense?.id != null) {
        expenseData['id'] = _expense!.id;
        
        await _supabaseService.client
            .from('expenses')
            .update(expenseData)
            .eq('id', _expense!.id!)
            .eq('user_id', currentUserId); // Ensure we only update our own expenses
            
        if (!mounted) return;
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Expense updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Creating new expense
        await _supabaseService.client
            .from('expenses')
            .insert(expenseData);
            
        if (!mounted) return;
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Expense created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving expense: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  DateTime _calculateNextDueDate() {
    final now = DateTime.now();
    
    switch (_frequency) {
      case ExpenseFrequency.oneTime:
        return _selectedDate;
      case ExpenseFrequency.weekly:
        return now.add(const Duration(days: 7));
      case ExpenseFrequency.monthly:
        return DateTime(now.year, now.month + 1, now.day);
      case ExpenseFrequency.quarterly:
        return DateTime(now.year, now.month + 3, now.day);
      case ExpenseFrequency.annually:
        return DateTime(now.year + 1, now.month, now.day);
      case ExpenseFrequency.custom:
        // Handle custom frequency if needed
        return _selectedDate;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isCreating = !_isEdit;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isCreating ? 'Add Expense' : 'Edit Expense'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: _isLoading
          ? const AppLoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        border: OutlineInputBorder(),
                        helperText: 'Minimum 3 characters',
                      ),
                      validator: _validateTitle,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: 'Amount',
                        border: OutlineInputBorder(),
                        prefixText: '\$ ',
                        helperText: 'Enter a positive number',
                      ),
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      validator: _validateAmount,
                    ),
                    const SizedBox(height: 16),
                    _buildDatePicker(),
                    const SizedBox(height: 16),
                    _buildCategoryDropdown(),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _vendorNameController,
                      decoration: const InputDecoration(
                        labelText: 'Vendor Name (Optional)',
                        border: OutlineInputBorder(),
                        helperText: 'Enter the name of the vendor or supplier',
                      ),
                      validator: _validateVendorName,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _receiptNumberController,
                      decoration: const InputDecoration(
                        labelText: 'Receipt Number (Optional)',
                        border: OutlineInputBorder(),
                        helperText: 'Enter the receipt or invoice number',
                      ),
                      validator: _validateReceiptNumber,
                    ),
                    const SizedBox(height: 16),
                    _buildRecurringSwitch(),
                    if (_isRecurring) ...[
                      const SizedBox(height: 16),
                      _buildFrequencyDropdown(),
                      const SizedBox(height: 16),
                      _buildOccurrencesField(),
                      const SizedBox(height: 16),
                      _buildEndDatePicker(),
                    ],
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 16),
                    const Text(
                      'Allocation (Optional)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildPropertyDropdown(),
                    if (_selectedProperty != null) ...[
                      const SizedBox(height: 16),
                      _buildRoomDropdown(),
                    ],
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description (Optional)',
                        border: OutlineInputBorder(),
                        alignLabelWithHint: true,
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveExpense,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          _isEdit ? 'Update Expense' : 'Save Expense',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildDatePicker() {
    final dateFormat = DateFormat('MMMM d, yyyy');
    
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: _selectedDate,
          firstDate: DateTime(2020),
          lastDate: DateTime(2030),
        );
        
        if (pickedDate != null) {
          setState(() {
            _selectedDate = pickedDate;
          });
        }
      },
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: 'Date',
          border: OutlineInputBorder(),
          suffixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(dateFormat.format(_selectedDate)),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<ExpenseCategoryModel>(
                decoration: const InputDecoration(
                  labelText: 'Category',
                  border: OutlineInputBorder(),
                ),
                value: _selectedCategory,
                items: _categories.map((category) {
                  return DropdownMenuItem<ExpenseCategoryModel>(
                    value: category,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: category.color,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(category.name),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a category';
                  }
                  return null;
                },
                isExpanded: true,
                hint: const Text('Select a category'),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.add_circle),
              tooltip: 'Create new category',
              onPressed: () {
                _showAddCategoryDialog();
              },
            ),
          ],
        ),
        if (_categories.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'No categories available. Create one using the + button.',
              style: TextStyle(color: Colors.red[700], fontSize: 12),
            ),
          ),
      ],
    );
  }

  void _showAddCategoryDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    Color selectedColor = Colors.blue;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (dialogContext, setDialogState) {
          return AlertDialog(
            title: const Text('Create Category'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Category Name',
                      border: OutlineInputBorder(),
                    ),
                    autofocus: true,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  const Text('Color:'),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildColorOption(Colors.red, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.pink, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.purple, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.deepPurple, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.indigo, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.blue, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.lightBlue, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.cyan, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.teal, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.green, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.lightGreen, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.lime, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.yellow, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.amber, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.orange, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.deepOrange, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.brown, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.grey, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                      _buildColorOption(Colors.blueGrey, selectedColor, (color) {
                        setDialogState(() {
                          selectedColor = color;
                        });
                      }),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(dialogContext);
                },
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (nameController.text.isEmpty) {
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      const SnackBar(
                        content: Text('Category name is required'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  try {
                    final newCategory = ExpenseCategoryModel(
                      name: nameController.text,
                      description: descriptionController.text.isEmpty ? null : descriptionController.text,
                      color: selectedColor,
                      userId: _supabaseService.client.auth.currentUser!.id,
                      isDefault: false,
                      createdAt: DateTime.now(),
                    );

                    Navigator.pop(dialogContext); // Close dialog before async operation
                    
                    // Create a map without the id field to avoid null id error
                    final categoryData = {
                      'name': newCategory.name,
                      'description': newCategory.description,
                      'color': newCategory.color.toHex(),
                      'user_id': newCategory.userId,
                      'is_default': newCategory.isDefault,
                      'created_at': newCategory.createdAt.toIso8601String(),
                    };
                    
                    final supabase = _supabaseService.client;
                    final response = await supabase
                        .from('expense_categories')
                        .insert(categoryData)
                        .select()
                        .single();
                    
                    final createdCategory = ExpenseCategoryModel.fromJson(response);
                    
                    if (!mounted) return;
                    
                    setState(() {
                      _categories.add(createdCategory);
                      _selectedCategory = createdCategory;
                    });

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Category created successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } catch (e) {
                    if (!mounted) return;
                    
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error creating category: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                child: const Text('Create'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildColorOption(Color color, Color selectedColor, Function(Color) onSelect) {
    final isSelected = color == selectedColor;
    
    return GestureDetector(
      onTap: () => onSelect(color),
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.black : Colors.transparent,
            width: 2,
          ),
        ),
        child: isSelected
            ? const Icon(
                Icons.check,
                color: Colors.white,
                size: 20,
              )
            : null,
      ),
    );
  }

  Widget _buildRecurringSwitch() {
    return SwitchListTile(
      title: const Text('Recurring Expense'),
      subtitle: const Text('Set up a recurring expense schedule'),
      value: _isRecurring,
      onChanged: (value) {
        setState(() {
          _isRecurring = value;
        });
      },
    );
  }

  Widget _buildFrequencyDropdown() {
    return DropdownButtonFormField<ExpenseFrequency>(
      decoration: const InputDecoration(
        labelText: 'Frequency',
        border: OutlineInputBorder(),
      ),
      value: _frequency,
      items: ExpenseFrequency.values.map((frequency) {
        return DropdownMenuItem<ExpenseFrequency>(
          value: frequency,
          child: Text(_getFrequencyDisplayName(frequency)),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _frequency = value!;
        });
      },
    );
  }

  String _getFrequencyDisplayName(ExpenseFrequency frequency) {
    switch (frequency) {
      case ExpenseFrequency.oneTime:
        return 'One-time';
      case ExpenseFrequency.weekly:
        return 'Weekly';
      case ExpenseFrequency.monthly:
        return 'Monthly';
      case ExpenseFrequency.quarterly:
        return 'Quarterly';
      case ExpenseFrequency.annually:
        return 'Annually';
      case ExpenseFrequency.custom:
        return 'Custom';
    }
  }

  Widget _buildOccurrencesField() {
    return TextFormField(
      controller: _occurrencesController,
      decoration: const InputDecoration(
        labelText: 'Number of Occurrences (Optional)',
        border: OutlineInputBorder(),
        helperText: 'Enter a positive number or leave blank for indefinite recurring',
      ),
      keyboardType: TextInputType.number,
      validator: _validateOccurrences,
    );
  }

  Widget _buildEndDatePicker() {
    final dateFormat = DateFormat('MMMM d, yyyy');
    
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: _selectedEndDate ?? DateTime.now().add(const Duration(days: 365)),
          firstDate: DateTime.now(),
          lastDate: DateTime(2030),
        );
        
        if (pickedDate != null) {
          setState(() {
            _selectedEndDate = pickedDate;
          });
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'End Date (Optional)',
          border: const OutlineInputBorder(),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_selectedEndDate != null)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _selectedEndDate = null;
                    });
                  },
                ),
              const Icon(Icons.calendar_today),
            ],
          ),
        ),
        child: _selectedEndDate != null
            ? Text(dateFormat.format(_selectedEndDate!))
            : const Text('No end date'),
      ),
    );
  }

  Widget _buildPropertyDropdown() {
    return DropdownButtonFormField<property_model.Property>(
      decoration: const InputDecoration(
        labelText: 'Property (Optional)',
        border: OutlineInputBorder(),
      ),
      value: _selectedProperty,
      items: _properties.map((property) {
        return DropdownMenuItem<property_model.Property>(
          value: property,
          child: Text(property.name),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedProperty = value;
          _selectedRoom = null;
          
          if (value != null) {
            _loadRooms(value.id);
          } else {
            _rooms = [];
          }
        });
      },
    );
  }

  Widget _buildRoomDropdown() {
    return DropdownButtonFormField<room_model.Room>(
      decoration: const InputDecoration(
        labelText: 'Room (Optional)',
        border: OutlineInputBorder(),
      ),
      value: _selectedRoom,
      items: _rooms.map((room) {
        return DropdownMenuItem<room_model.Room>(
          value: room,
          child: Text(room.name),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedRoom = value;
        });
      },
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _occurrencesController.dispose();
    _vendorNameController.dispose();
    _receiptNumberController.dispose();
    super.dispose();
  }
} 