-- Drop existing objects first to make the migration idempotent
DROP POLICY IF EXISTS "Allow authenticated users to read billing_metadata" ON billing_metadata;
DROP POLICY IF EXISTS "Allow authenticated users to update billing_metadata" ON billing_metadata;
DROP POLICY IF EXISTS "Allow authenticated users to insert into billing_metadata" ON billing_metadata;
DROP POLICY IF EXISTS "Allow service_role full access to billing_metadata" ON billing_metadata;
DROP TRIGGER IF EXISTS trigger_update_billing_metadata_updated_at ON billing_metadata;
DROP FUNCTION IF EXISTS update_billing_metadata_updated_at();

-- Create billing_metadata table for tracking bill number sequences
CREATE TABLE IF NOT EXISTS billing_metadata (
    id TEXT PRIMARY KEY,
    last_bill_number INTEGER NOT NULL DEFAULT 1000000,
    last_group_bill_number INTEGER NOT NULL DEFAULT 1000000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial record for bill number tracking
INSERT INTO billing_metadata (id, last_bill_number, last_group_bill_number)
VALUES ('bill_number_tracker', 1000000, 1000000)
ON CONFLICT (id) DO NOTHING;

-- Create a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_billing_metadata_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

CREATE TRIGGER trigger_update_billing_metadata_updated_at
    BEFORE UPDATE ON billing_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_billing_metadata_updated_at();

-- Grant necessary permissions
GRANT ALL ON billing_metadata TO authenticated;
GRANT ALL ON billing_metadata TO service_role;

-- Enable Row Level Security
ALTER TABLE billing_metadata ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Allow authenticated users to read billing_metadata"
    ON billing_metadata FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow authenticated users to update billing_metadata"
    ON billing_metadata FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow authenticated users to insert into billing_metadata"
    ON billing_metadata FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Allow service_role full access to billing_metadata"
    ON billing_metadata
    TO service_role
    USING (true)
    WITH CHECK (true);
