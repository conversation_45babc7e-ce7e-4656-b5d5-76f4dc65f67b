import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/settings/settings_model.dart';

class SettingsDatabaseService {
  final SupabaseClient _client;

  // Database table name
  static const String _tableName = 'user_settings';

  SettingsDatabaseService(this._client);

  // Get user settings from the database
  Future<UserSettings?> getUserSettings(String userId) async {
    try {
      debugPrint('Fetching settings for user: $userId');
      final response =
          await _client.from(_tableName).select().eq('id', userId).single();
      debugPrint('Settings fetch response: $response');
      return UserSettings.fromJson(response);
    } catch (e) {
      debugPrint('Error fetching user settings: $e');
      return null;
    }
  }

  // Save user settings to the database (creates or updates)
  Future<bool> saveUserSettings(UserSettings settings) async {
    try {
      debugPrint('Saving settings for user: ${settings.id}');
      debugPrint('Settings data: ${settings.toJson()}');
      
      // Upsert - insert if not exists, update if exists
      final response = await _client.from(_tableName).upsert(settings.toJson()).select();
      debugPrint('Settings save response: $response');
      return true;
    } catch (e) {
      debugPrint('Error saving user settings: $e');
      // Print more detailed error information
      if (e is PostgrestException) {
        debugPrint('PostgrestException code: ${e.code}');
        debugPrint('PostgrestException message: ${e.message}');
        debugPrint('PostgrestException details: ${e.details}');
      }
      return false;
    }
  }

  // Update specific setting fields
  Future<bool> updateCurrency(String userId, String currencyCode) async {
    try {
      debugPrint('Updating currency for user: $userId to $currencyCode');
      final response = await _client
          .from(_tableName)
          .update({
            'currency_code': currencyCode,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
      debugPrint('Currency update response: $response');
      return true;
    } catch (e) {
      debugPrint('Error updating currency: $e');
      return false;
    }
  }

  Future<bool> updateLanguage(String userId, String languageCode) async {
    try {
      debugPrint('Updating language for user: $userId to $languageCode');
      final response = await _client
          .from(_tableName)
          .update({
            'language_code': languageCode,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
      debugPrint('Language update response: $response');
      return true;
    } catch (e) {
      debugPrint('Error updating language: $e');
      return false;
    }
  }

  // Delete user settings (e.g., when user logs out or deletes account)
  Future<bool> deleteUserSettings(String userId) async {
    try {
      await _client.from(_tableName).delete().eq('id', userId);
      return true;
    } catch (e) {
      debugPrint('Error deleting user settings: $e');
      return false;
    }
  }

  // Check if the user_settings table exists
  Future<bool> checkIfTableExists() async {
    try {
      // Try to query the table
      await _client.from(_tableName).select('id').limit(1);
      return true;
    } catch (e) {
      // If we get an error, the table likely doesn't exist
      debugPrint('Table $_tableName may not exist: $e');
      return false;
    }
  }
}
