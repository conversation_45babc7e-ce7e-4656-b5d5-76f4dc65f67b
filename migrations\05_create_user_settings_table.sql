-- Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  currency_code VARCHAR(10) NOT NULL DEFAULT 'KSH',
  language_code VARCHAR(10) NOT NULL DEFAULT 'en',
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Add RLS (Row Level Security) policies
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can read their own settings" ON user_settings;
DROP POLICY IF EXISTS "Users can update their own settings" ON user_settings;
DROP POLICY IF EXISTS "Users can insert their own settings" ON user_settings;
DROP POLICY IF EXISTS "Users can delete their own settings" ON user_settings;

-- Policy: Users can only read their own settings
CREATE POLICY "Users can read their own settings" 
ON user_settings FOR SELECT 
USING (id = (select auth.uid()));

-- Policy: Users can only update their own settings
CREATE POLICY "Users can update their own settings" 
ON user_settings FOR UPDATE 
USING (id = (select auth.uid()));

-- Policy: Users can only insert their own settings
CREATE POLICY "Users can insert their own settings" 
ON user_settings FOR INSERT 
WITH CHECK (id = (select auth.uid()));

-- Policy: Users can only delete their own settings
CREATE POLICY "Users can delete their own settings" 
ON user_settings FOR DELETE 
USING (id = (select auth.uid()));

-- Allow public (authenticated) access
GRANT SELECT, INSERT, UPDATE, DELETE ON user_settings TO authenticated;

-- Create trigger to update 'updated_at' on changes
DROP FUNCTION IF EXISTS update_timestamp CASCADE;
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

DROP TRIGGER IF EXISTS update_user_settings_timestamp ON user_settings;
CREATE TRIGGER update_user_settings_timestamp
BEFORE UPDATE ON user_settings
FOR EACH ROW
EXECUTE FUNCTION update_timestamp(); 