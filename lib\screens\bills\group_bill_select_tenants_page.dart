import 'package:flutter/material.dart';
import '../../models/bill/bill.dart';
import '../../models/tenant/tenant.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';
import '../../utils/logger.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/common/section_header.dart';
import '../../widgets/error_dialog.dart';

// Helper function to show error dialog
void showErrorDialog(BuildContext context, String message) {
  ErrorDialog.show(
    context,
    title: 'Error',
    message: message,
  );
}

class GroupBillSelectTenantsPage extends StatefulWidget {
  final Bill bill;
  final Bill? existingBill;
  final bool isSplitEvenly;
  
  const GroupBillSelectTenantsPage({
    super.key,
    required this.bill,
    this.existingBill,
    required this.isSplitEvenly,
  });

  @override
  State<GroupBillSelectTenantsPage> createState() => _GroupBillSelectTenantsPageState();
}

class _GroupBillSelectTenantsPageState extends State<GroupBillSelectTenantsPage> {
  bool _isLoading = false;
  
  // For tenant selection
  List<Tenant> _availableTenants = [];
  List<Tenant> _selectedTenants = [];
  
  // For tenant filtering and search
  String _searchQuery = '';
  String _filterProperty = 'all';
  String _sortBy = 'name';
  bool _sortAscending = true;
  
  // For property filtering
  List<Property> _properties = [];
  Map<String, Property> _roomToPropertyMap = {};
  Map<String, String> _tenantPropertyNames = {};
  Map<String, String> _tenantRoomNames = {}; // Maps tenant ID to room name
  
  // For bill details
  Bill? _createdBill;

  @override
  void initState() {
    super.initState();
    _loadTenants();
    _loadProperties();
  }

  // Load all active tenants
  Future<void> _loadTenants() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final tenantService = serviceLocator.tenantService;
      final tenants = await tenantService.getAllTenants();
      
      // Only include active tenants with assigned rooms
      final activeTenants = tenants
          .where((tenant) => 
              tenant.status == TenantStatus.active && 
              tenant.roomId != null)
          .toList();
      
      setState(() {
        _availableTenants = activeTenants;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Error',
          message: 'Failed to load tenants: ${e.toString()}'
        );
      }
    }
  }
  
  // Load all properties and create a mapping between rooms and properties
  Future<void> _loadProperties() async {
    try {
      final propertyService = serviceLocator.propertyService;
      final roomService = serviceLocator.roomService;
      
      // Load all properties
      final properties = await propertyService.getAllProperties();
      
      // Create maps for roomId to property and room details
      final roomToPropertyMap = <String, Property>{};
      final tenantPropertyNames = <String, String>{};
      final tenantRoomNames = <String, String>{};
      
      // For each property, load its rooms and map them
      for (final property in properties) {
        final rooms = await roomService.getRoomsByPropertyId(property.id);
        for (final room in rooms) {
          roomToPropertyMap[room.id] = property;
        }
      }
      
      // Map tenants to property and room names
      for (final tenant in _availableTenants) {
        if (tenant.roomId != null && roomToPropertyMap.containsKey(tenant.roomId)) {
          final property = roomToPropertyMap[tenant.roomId]!;
          tenantPropertyNames[tenant.id] = property.name;
          
          // Load room details to get the room name
          try {
            final room = await roomService.getRoomById(tenant.roomId!);
            if (room != null) {
              tenantRoomNames[tenant.id] = room.name;
            }
          } catch (e) {
            AppLogger.warning('Failed to load room details for tenant ${tenant.id}: $e');
          }
        }
      }
      
      setState(() {
        _properties = properties;
        _roomToPropertyMap = roomToPropertyMap;
        _tenantPropertyNames = tenantPropertyNames;
        _tenantRoomNames = tenantRoomNames;
      });
    } catch (e) {
      if (mounted) {
        showErrorDialog(context, 'Failed to load properties: ${e.toString()}');
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Tenants for Bill'),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBillSummary(),
                  const SizedBox(height: 24),
                  _buildTenantSelectionSection(),
                  if (widget.isSplitEvenly && _selectedTenants.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildSplitAmountPreview(),
                  ],
                  const SizedBox(height: 32),
                  _buildSubmitButton(),
                ],
              ),
            ),
    );
  }
  
  Widget _buildBillSummary() {
    final settingsService = serviceLocator.settingsService;
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SectionHeader(title: 'Bill Summary'),
            const SizedBox(height: 12),
            _buildInfoRow('Title', widget.bill.title),
            _buildInfoRow('Amount', settingsService.formatCurrency(widget.bill.amount)),
            _buildInfoRow('Due Date', '${widget.bill.dueDate.day}/${widget.bill.dueDate.month}/${widget.bill.dueDate.year}'),
            _buildInfoRow('Bill Type', _getBillTypeText(widget.bill.type)),
            _buildInfoRow('Split Method', widget.isSplitEvenly ? 'Split Evenly' : 'Individual Amounts'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
  
  String _getBillTypeText(BillType type) {
    switch (type) {
      case BillType.utility:
        return 'Utility';
      case BillType.maintenance:
        return 'Maintenance';
      case BillType.service:
        return 'Service';
      case BillType.rent:
        return 'Rent';
      case BillType.other:
        return 'Other';
    }
  }
  
  Widget _buildTenantSelectionSection() {
    // Filter and sort tenants based on current criteria
    List<Tenant> filteredTenants = _filterAndSortTenants();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Select Tenants'),
        const SizedBox(height: 8),
        
        // Search and filter bar
        Card(
          elevation: 2,
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search field
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Search tenants...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    // Filter dropdown
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Filter by Property',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        ),
                        value: _filterProperty,
                        items: [
                          const DropdownMenuItem(value: 'all', child: Text('All Properties')),
                          ..._properties.map((property) => 
                            DropdownMenuItem(value: property.id, child: Text(property.name))
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _filterProperty = value;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Sort dropdown
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Sort by',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'name', child: Text('Name')),
                          DropdownMenuItem(value: 'property', child: Text('Property')),
                          DropdownMenuItem(value: 'room', child: Text('Room')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              if (_sortBy == value) {
                                _sortAscending = !_sortAscending;
                              } else {
                                _sortBy = value;
                                _sortAscending = true;
                              }
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Sort direction toggle
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text('Sort direction: ${_sortAscending ? 'Ascending' : 'Descending'}'),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward),
                      onPressed: () {
                        setState(() {
                          _sortAscending = !_sortAscending;
                        });
                      },
                      tooltip: 'Toggle sort direction',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        // Selection actions bar
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected: ${_selectedTenants.length} ${_selectedTenants.length == 1 ? 'tenant' : 'tenants'}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                TextButton.icon(
                  onPressed: _availableTenants.isNotEmpty ? () {
                    setState(() {
                      _selectedTenants = List.from(_availableTenants);
                    });
                  } : null,
                  icon: const Icon(Icons.select_all),
                  label: const Text('Select All'),
                ),
                TextButton.icon(
                  onPressed: _selectedTenants.isNotEmpty ? () {
                    setState(() {
                      _selectedTenants.clear();
                    });
                  } : null,
                  icon: const Icon(Icons.deselect),
                  label: const Text('Clear'),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Divider
        const Divider(),
        const SizedBox(height: 8),
        
        // Available Tenants List
        if (_availableTenants.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('No active tenants found'),
            ),
          )
        else if (filteredTenants.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(_searchQuery.isNotEmpty 
                ? 'No tenants match your search for "$_searchQuery"'
                : _filterProperty != 'all'
                  ? 'No tenants found in the selected property'
                  : 'No tenants match your criteria'
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            height: 350, // Fixed height for scrollable container
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: filteredTenants.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final tenant = filteredTenants[index];
                final isSelected = _selectedTenants.any((t) => t.id == tenant.id);
                
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  elevation: isSelected ? 2 : 1,
                  child: CheckboxListTile(
                    title: Text(
                      '${tenant.firstName} ${tenant.lastName}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_tenantRoomNames.containsKey(tenant.id))
                          Row(
                            children: [
                              Icon(Icons.home, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                'House: ${_tenantRoomNames[tenant.id]}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        if (_tenantPropertyNames.containsKey(tenant.id))
                          Row(
                            children: [
                              Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  'Property: ${_tenantPropertyNames[tenant.id]}',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context).colorScheme.secondary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        if (tenant.phoneNumber != null && tenant.phoneNumber!.isNotEmpty)
                          Row(
                            children: [
                              Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                tenant.phoneNumber!,
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            ],
                          ),
                      ],
                    ),
                    secondary: CircleAvatar(
                      backgroundColor: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey[400],
                      child: Text(
                        '${tenant.firstName[0]}${tenant.lastName[0]}',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    value: isSelected,
                    onChanged: (selected) {
                      setState(() {
                        if (selected == true) {
                          if (!_selectedTenants.any((t) => t.id == tenant.id)) {
                            _selectedTenants.add(tenant);
                          }
                        } else {
                          _selectedTenants.removeWhere((t) => t.id == tenant.id);
                        }
                      });
                    },
                  ),
                );
              },
            ),
          ),
          
        const SizedBox(height: 16),
        
        // Selected tenants chips
        if (_selectedTenants.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Selected Tenants:',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedTenants.map((tenant) {
                  return Chip(
                    avatar: CircleAvatar(
                      backgroundColor: Theme.of(context).colorScheme.secondary,
                      child: Text(
                        '${tenant.firstName[0]}${tenant.lastName[0]}',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                    label: Text('${tenant.firstName} ${tenant.lastName}'),
                    deleteIcon: const Icon(Icons.cancel, size: 18),
                    onDeleted: () {
                      setState(() {
                        _selectedTenants.removeWhere((t) => t.id == tenant.id);
                      });
                    },
                  );
                }).toList(),
              ),
            ],
          ),
      ],
    );
  }
  
  // Helper method to filter and sort tenants
  List<Tenant> _filterAndSortTenants() {
    // First apply filters
    List<Tenant> filtered = _availableTenants.where((tenant) {
      // Apply search query filter
      if (_searchQuery.isNotEmpty) {
        final fullName = '${tenant.firstName} ${tenant.lastName}'.toLowerCase();
        final phone = tenant.phoneNumber?.toLowerCase() ?? '';
        final property = _tenantPropertyNames[tenant.id]?.toLowerCase() ?? '';
        final room = _tenantRoomNames[tenant.id]?.toLowerCase() ?? '';
        
        if (!fullName.contains(_searchQuery) && 
            !phone.contains(_searchQuery) &&
            !property.contains(_searchQuery) &&
            !room.contains(_searchQuery)) {
          return false;
        }
      }
      
      // Apply property filter if not "all"
      if (_filterProperty != 'all') {
        // Check if tenant has a room and if that room belongs to the selected property
        if (tenant.roomId == null) return false;
        
        final property = _roomToPropertyMap[tenant.roomId];
        return property != null && property.id == _filterProperty;
      }
      
      return true;
    }).toList();
    
    // Then apply sorting
    filtered.sort((a, b) {
      int result;
      switch (_sortBy) {
        case 'name':
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
          break;
        case 'property':
          final aProperty = _tenantPropertyNames[a.id] ?? '';
          final bProperty = _tenantPropertyNames[b.id] ?? '';
          result = aProperty.compareTo(bProperty);
          break;
        case 'room':
          final aRoom = _tenantRoomNames[a.id] ?? '';
          final bRoom = _tenantRoomNames[b.id] ?? '';
          result = aRoom.compareTo(bRoom);
          break;
        default:
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
      }
      
      return _sortAscending ? result : -result;
    });
    
    return filtered;
  }
  
  Widget _buildSplitAmountPreview() {
    final splitAmount = _calculateSplitAmount();
    final settingsService = serviceLocator.settingsService;
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Text(
        'Each tenant will be billed: ${settingsService.formatCurrency(splitAmount)}',
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _selectedTenants.isEmpty ? null : _saveBill,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Text(widget.existingBill != null ? 'Update Bill' : 'Create Bill'),
      ),
    );
  }
  
  double _calculateSplitAmount() {
    if (_selectedTenants.isEmpty) {
      return 0.0;
    }
    
    final totalAmount = widget.bill.amount;
    return totalAmount / _selectedTenants.length;
  }
  
  Future<void> _saveBill() async {
    if (_selectedTenants.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one tenant')),
      );
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Create or update the bill
      if (widget.existingBill != null) {
        // Update existing bill
        final updatedBill = widget.existingBill!.copyWith(
          title: widget.bill.title,
          description: widget.bill.description,
          amount: widget.bill.amount,
          dueDate: widget.bill.dueDate,
          type: widget.bill.type,
          recurrence: widget.bill.recurrence,
          notes: widget.bill.notes,
          updatedAt: DateTime.now(),
        );
        
        _createdBill = await serviceLocator.billService.updateBill(updatedBill);
        
        // Delete all existing tenant relations and create new ones
        await serviceLocator.billTenantService.deleteRelationsForBill(_createdBill!.id);
      } else {
        // Create new bill
        _createdBill = await serviceLocator.billService.createBill(widget.bill);
      }
      
      // Create tenant relations
      final tenantIds = _selectedTenants.map((t) => t.id).toList();
      
      await serviceLocator.billTenantService.createMultipleRelations(
        _createdBill!.id,
        tenantIds,
        splitEvenly: widget.isSplitEvenly,
        billAmount: widget.bill.amount,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(widget.existingBill != null 
              ? 'Bill updated successfully' 
              : 'Bill created and assigned to ${_selectedTenants.length} tenants')),
        );
        
        // Close both pages and return to the bills list
        Navigator.of(context).pop();
        Navigator.of(context).pop(_createdBill);
      }
    } catch (e) {
      AppLogger.error('Error saving bill: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 