-- Drop existing view if it exists
DROP VIEW IF EXISTS tenant_bills_view;

-- Create the enhanced tenant_bills_view with automatic overdue status calculation
CREATE OR REPLACE VIEW tenant_bills_view 
WITH (security_invoker=true)
AS
SELECT 
  bt.id AS relation_id,
  bt.bill_id,
  bt.tenant_id,
  bt.split_amount,
  b.title AS bill_title,
  b.description AS bill_description,
  b.amount AS bill_amount,
  b.due_date,
  b.status AS bill_status,
  b.type AS bill_type,
  b.recurrence AS bill_recurrence,
  b.property_id,
  b.room_id,
  b.bill_number,
  b.paid_at,
  b.paid_amount,
  b.notes,
  b.created_at,
  b.updated_at,
  b.include_in_rent,
  b.utility_type,
  b.previous_meter_reading,
  b.current_meter_reading,
  b.unit_consumed,
  b.rate_per_unit,
  b.bill_components,
  (t.first_name || ' ' || t.last_name) AS tenant_name,
  t.email AS tenant_email,
  CASE
    WHEN b.status = 'paid' THEN false
    WHEN b.status = 'cancelled' THEN false
    WHEN b.due_date < now() THEN true
    ELSE false
  END AS is_overdue
FROM 
  bill_tenants bt
JOIN 
  bills b ON bt.bill_id = b.id
JOIN 
  tenants t ON bt.tenant_id = t.id;

-- Create function to automatically update bill status to 'overdue' when due date has passed
CREATE OR REPLACE FUNCTION update_overdue_bills()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE bills
  SET status = 'overdue'
  WHERE 
    status = 'pending' AND 
    due_date < CURRENT_DATE;
  
  -- Log the update
  INSERT INTO activity_logs (type, action, details)
  VALUES ('system', 'update_overdue_bills', json_build_object(
    'updated_count', (SELECT count(*) FROM bills WHERE status = 'overdue'),
    'run_at', now()
  ));
END;
$$;

-- Create a trigger to automatically update bill status on access
CREATE OR REPLACE FUNCTION check_bill_overdue_on_select()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- If the bill is pending and past due date, update it to overdue
  IF NEW.bill_status = 'pending' AND NEW.due_date < CURRENT_DATE THEN
    UPDATE bills SET status = 'overdue' WHERE id = NEW.bill_id;
    NEW.bill_status := 'overdue';
  END IF;
  
  RETURN NEW;
END;
$$;

-- Fix the update_bill_status function that's missing a search_path parameter
DROP FUNCTION IF EXISTS public.update_bill_status();
CREATE OR REPLACE FUNCTION public.update_bill_status()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- If bill is not paid or cancelled and due date has passed, mark as overdue
  IF NEW.status NOT IN ('paid', 'cancelled') AND NEW.due_date < NOW() THEN
    NEW.status := 'overdue';
  END IF;
  RETURN NEW;
END;
$$;

-- Attach the trigger to the view
DROP TRIGGER IF EXISTS check_overdue_bills_on_select ON tenant_bills_view;
CREATE TRIGGER check_overdue_bills_on_select
INSTEAD OF SELECT ON tenant_bills_view
FOR EACH ROW
EXECUTE FUNCTION check_bill_overdue_on_select();

-- Add comment explaining purpose of this view
COMMENT ON VIEW tenant_bills_view IS 'Provides a secure view of bills for tenants with automatic overdue status calculation. Used by the tenant portal to display bills and bill-tenant relationships.'; 