import 'package:currency_picker/currency_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/settings/settings_model.dart';
import 'settings/settings_database_service.dart';

class SettingsService with ChangeNotifier {
  static const String _currencyCodeKey = 'currency_code';
  static const String _localeKey = 'locale_code';
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  final SettingsDatabaseService _settingsDatabase;

  // Default currency is USD initially, will be updated based on locale
  Currency _selectedCurrency = CurrencyService().findByCode('USD')!;

  // Default locale is English
  Locale _currentLocale = const Locale('en');

  // Constructor with database dependency
  SettingsService(this._settingsDatabase);

  Currency get selectedCurrency => _selectedCurrency;
  Locale get currentLocale => _currentLocale;

  // Initialize settings
  Future<void> init() async {
    try {
      // Try to get current user ID
      final userId = _getUserId();

      if (userId != null) {
        // Try to load settings from database first
        final dbSettings = await _settingsDatabase.getUserSettings(userId);

        if (dbSettings != null) {
          // Use settings from database
          _currentLocale = Locale(dbSettings.languageCode);
          final currency = CurrencyService().findByCode(
            dbSettings.currencyCode,
          );
          if (currency != null) {
            _selectedCurrency = currency;
          }

          // Sync settings to secure storage as well
          await _storage.write(key: _localeKey, value: dbSettings.languageCode);
          await _storage.write(
            key: _currencyCodeKey,
            value: dbSettings.currencyCode,
          );
          return;
        }
      }

      // Fall back to secure storage if database fetch fails or not logged in
      await _loadFromSecureStorage();

      // If logged in, save local settings to database
      if (userId != null) {
        await _saveSettingsToDatabase(userId);
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
      // Last resort - fall back to secure storage if everything else fails
      await _loadFromSecureStorage();
    }
  }

  // Save current settings to database
  Future<bool> _saveSettingsToDatabase(String userId) async {
    try {
      final settings = UserSettings(
        id: userId,
        currencyCode: _selectedCurrency.code,
        languageCode: _currentLocale.languageCode,
        updatedAt: DateTime.now(),
      );
      return await _settingsDatabase.saveUserSettings(settings);
    } catch (e) {
      debugPrint('Error saving settings to database: $e');
      return false;
    }
  }

  // Manually save settings to database (can be called from UI)
  Future<bool> saveSettingsToDatabase() async {
    final userId = _getUserId();
    if (userId == null) {
      return false;
    }
    return await _saveSettingsToDatabase(userId);
  }

  // Load settings from secure storage
  Future<void> _loadFromSecureStorage() async {
    // Load saved locale if it exists
    final savedLocale = await _storage.read(key: _localeKey);
    if (savedLocale != null) {
      _currentLocale = Locale(savedLocale);
    } else {
      // Try to get the device locale
      final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;
      // Check if the device locale is supported
      if (['en', 'sw'].contains(deviceLocale.languageCode)) {
        _currentLocale = deviceLocale;
        await _storage.write(key: _localeKey, value: deviceLocale.languageCode);
      }
    }

    // Load saved currency if it exists
    final savedCurrencyCode = await _storage.read(key: _currencyCodeKey);
    if (savedCurrencyCode != null) {
      final currency = CurrencyService().findByCode(savedCurrencyCode);
      if (currency != null) {
        _selectedCurrency = currency;
      }
    } else {
      // Set default currency based on locale/country
      _selectedCurrency = _getDefaultCurrencyForLocale(_currentLocale);
      await _storage.write(
        key: _currencyCodeKey,
        value: _selectedCurrency.code,
      );
    }
  }

  // Get the current user ID if logged in
  String? _getUserId() {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      return user?.id;
    } catch (e) {
      debugPrint('Error getting user ID: $e');
      return null;
    }
  }

  // Change currency
  Future<void> setCurrency(Currency currency) async {
    _selectedCurrency = currency;
    // Save to secure storage
    await _storage.write(key: _currencyCodeKey, value: currency.code);

    // Save to database if user is logged in
    final userId = _getUserId();
    if (userId != null) {
      await _settingsDatabase.updateCurrency(userId, currency.code);
    }
    notifyListeners();
  }

  // Change locale
  Future<void> setLocale(Locale locale) async {
    _currentLocale = locale;
    // Save to secure storage
    await _storage.write(key: _localeKey, value: locale.languageCode);

    // Save to database if user is logged in
    final userId = _getUserId();
    if (userId != null) {
      await _settingsDatabase.updateLanguage(userId, locale.languageCode);
    }
    notifyListeners();
  }

  // Format amount with currency symbol and thousands separator
  String formatCurrency(double amount) {
    final formatCurrency = NumberFormat.currency(
      symbol: _selectedCurrency.symbol,
      decimalDigits: 2,
    );

    return formatCurrency.format(amount);
  }

  // Format amount with just thousands separator (no currency symbol)
  String formatNumber(double amount) {
    final formatter = NumberFormat("#,##0.00", "en_US");
    return formatter.format(amount);
  }

  // Get default currency based on locale
  Currency _getDefaultCurrencyForLocale(Locale locale) {
    // For Kenya or Swahili language, use Kenya Shillings
    if (locale.countryCode == 'KE' || locale.languageCode == 'sw') {
      return CurrencyService().findByCode('KES') ??
          CurrencyService().findByCode('USD')!;
    }

    // Map common country codes to currencies
    final Map<String, String> countryCurrencyMap = {
      'US': 'USD', // United States - US Dollar
      'GB': 'GBP', // United Kingdom - British Pound
      'EU': 'EUR', // European Union - Euro
      'JP': 'JPY', // Japan - Japanese Yen
      'CN': 'CNY', // China - Chinese Yuan
      'IN': 'INR', // India - Indian Rupee
      'NG': 'NGN', // Nigeria - Nigerian Naira
      'ZA': 'ZAR', // South Africa - South African Rand
      'TZ': 'TZS', // Tanzania - Tanzanian Shilling
      'UG': 'UGX', // Uganda - Ugandan Shilling
      'RW': 'RWF', // Rwanda - Rwandan Franc
    };

    if (locale.countryCode != null &&
        countryCurrencyMap.containsKey(locale.countryCode)) {
      return CurrencyService().findByCode(
            countryCurrencyMap[locale.countryCode]!,
          ) ??
          CurrencyService().findByCode('USD')!;
    }

    // Default to USD if no match
    return CurrencyService().findByCode('USD')!;
  }
}
