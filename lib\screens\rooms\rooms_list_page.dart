import 'package:flutter/material.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../widgets/navigation/app_drawer.dart';
import '../../utils/currency_formatter.dart';
import 'room_detail_page.dart';
import 'room_form_page.dart';

// Helper class to track item types and indices
class _ListItemInfo {
  final bool isHeader;
  final String? propertyId;
  final int? roomIndex;

  _ListItemInfo({required this.isHeader, this.propertyId, this.roomIndex});
}

class RoomsListPage extends StatefulWidget {
  const RoomsListPage({super.key});

  @override
  State<RoomsListPage> createState() => _RoomsListPageState();
}

class _RoomsListPageState extends State<RoomsListPage> {
  List<Room> _rooms = [];
  List<Property> _properties = [];
  bool _isLoading = true;

  // Property filter
  String? _selectedPropertyId;

  // Search and filtering
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name'; // Default sort
  RoomOccupancyStatus? _filterByStatus;
  String? _filterByRoomType;

  // Get list of unique room types from rooms
  List<String> get _availableRoomTypes =>
      _rooms.map((r) => r.roomTypeName).toSet().toList()..sort();

  @override
  void initState() {
    super.initState();
    _loadData();

    // Listen to search query changes
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get rooms and properties from services
      final rooms = await serviceLocator.roomService.getAllRooms();
      final properties =
          await serviceLocator.propertyService.getAllProperties();

      if (mounted) {
        setState(() {
          _rooms = rooms;
          _properties = properties;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _rooms = [];
          _properties = [];
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Get filtered rooms with all filters applied
  List<Room> get filteredRooms {
    // First apply property filter
    List<Room> filtered =
        _selectedPropertyId == null
            ? List.from(_rooms)
            : _rooms
                .where((room) => room.propertyId == _selectedPropertyId)
                .toList();

    // Apply search
    if (_searchQuery.isNotEmpty) {
      filtered =
          filtered.where((room) {
            return room.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                room.roomTypeName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                );
          }).toList();
    }

    // Apply status filter
    if (_filterByStatus != null) {
      filtered =
          filtered
              .where((room) => room.occupancyStatus == _filterByStatus)
              .toList();
    }

    // Apply room type filter
    if (_filterByRoomType != null) {
      filtered =
          filtered
              .where((room) => room.roomTypeName == _filterByRoomType)
              .toList();
    }

    // Apply sort
    switch (_sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'price_high':
        filtered.sort((a, b) => b.rentalPrice.compareTo(a.rentalPrice));
        break;
      case 'price_low':
        filtered.sort((a, b) => a.rentalPrice.compareTo(b.rentalPrice));
        break;
      case 'type':
        filtered.sort((a, b) => a.roomTypeName.compareTo(b.roomTypeName));
        break;
    }

    // If grouping by property, ensure they're sorted by property first
    if (_sortBy != 'property' && _selectedPropertyId == null) {
      // First sort by our selected sort method, then group by properties
      final Map<String, List<Room>> roomsByProperty = {};

      // Group rooms by property
      for (final room in filtered) {
        if (!roomsByProperty.containsKey(room.propertyId)) {
          roomsByProperty[room.propertyId] = [];
        }
        roomsByProperty[room.propertyId]!.add(room);
      }

      // Flatten the map back into a list, preserving property grouping
      filtered = roomsByProperty.values.expand((rooms) => rooms).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rooms'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadData,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Search and filter section
                  _buildSearchAndFilterSection(),

                  // Rooms list
                  Expanded(child: _buildRoomsList()),
                ],
              ),
      floatingActionButton: FloatingActionButton(
        tooltip: 'Add Room',
        onPressed: _addRoom,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Column(
        children: [
          // Search bar
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withAlpha(38),
                  spreadRadius: 1,
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search rooms...',
                prefixIcon: const Icon(Icons.search, size: 20),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade200),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade200),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).primaryColor),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
                suffixIcon:
                    _searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear, size: 20),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                        : null,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Property filter - styled like in screenshot
          if (_properties.isNotEmpty)
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Label
                  Padding(
                    padding: const EdgeInsets.only(left: 12, top: 6, bottom: 4),
                    child: Text(
                      'Filter by Property',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      _showPropertySelector(context);
                    },
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(12, 0, 12, 6),
                      child: Row(
                        children: [
                          const Icon(Icons.home, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _selectedPropertyId != null
                                  ? _properties
                                      .firstWhere(
                                        (p) => p.id == _selectedPropertyId,
                                      )
                                      .name
                                  : 'All Properties',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const Icon(Icons.arrow_drop_down),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 8),

          // Filters row - Status and Type
          Row(
            children: [
              // Status filter
              Expanded(
                child: InkWell(
                  onTap: () {
                    _showStatusSelector(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.white,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            _filterByStatus != null
                                ? _filterByStatus.toString().split('.').last
                                : 'All Statuses',
                            style: TextStyle(
                              fontWeight:
                                  _filterByStatus != null
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // Room Type filter
              Expanded(
                child: InkWell(
                  onTap: () {
                    _showRoomTypeSelector(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.white,
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            _filterByRoomType != null
                                ? _filterByRoomType!
                                : 'All Types',
                            style: TextStyle(
                              fontWeight:
                                  _filterByRoomType != null
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Sort option
          InkWell(
            onTap: () {
              _showSortOptionSelector(context);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _getSortByDisplayText(),
                      style: const TextStyle(fontWeight: FontWeight.normal),
                    ),
                  ),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper to display sort option in a readable format
  String _getSortByDisplayText() {
    switch (_sortBy) {
      case 'name':
        return 'Name (A-Z)';
      case 'price_high':
        return 'Price (High to Low)';
      case 'price_low':
        return 'Price (Low to High)';
      case 'type':
        return 'Room Type';
      default:
        return 'Name (A-Z)';
    }
  }

  // Property selector bottom sheet
  void _showPropertySelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Select Property',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
              ),
              const Divider(height: 0),
              Expanded(
                child: ListView(
                  children: [
                    // All properties option
                    ListTile(
                      title: const Text('All Properties'),
                      leading: const Icon(Icons.home_work),
                      selected: _selectedPropertyId == null,
                      onTap: () {
                        setState(() {
                          _selectedPropertyId = null;
                        });
                        Navigator.pop(context);
                      },
                    ),
                    // Individual properties
                    ..._properties.map((property) {
                      return ListTile(
                        title: Text(property.name),
                        subtitle: Text(property.address),
                        leading: const Icon(Icons.home),
                        selected: _selectedPropertyId == property.id,
                        onTap: () {
                          setState(() {
                            _selectedPropertyId = property.id;
                          });
                          Navigator.pop(context);
                        },
                      );
                    }),
                  ],
                ),
              ),
            ],
          ),
    );
  }

  // Status selector bottom sheet
  void _showStatusSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Select Status',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
              ),
              const Divider(height: 0),
              // All statuses option
              ListTile(
                title: const Text('All Statuses'),
                selected: _filterByStatus == null,
                onTap: () {
                  setState(() {
                    _filterByStatus = null;
                  });
                  Navigator.pop(context);
                },
              ),
              // Individual statuses
              ...RoomOccupancyStatus.values.map((status) {
                Color statusColor;
                IconData statusIcon;

                switch (status) {
                  case RoomOccupancyStatus.vacant:
                    statusColor = Colors.green;
                    statusIcon = Icons.check_circle;
                    break;
                  case RoomOccupancyStatus.occupied:
                    statusColor = Colors.blue;
                    statusIcon = Icons.person;
                    break;
                  case RoomOccupancyStatus.reserved:
                    statusColor = Colors.orange;
                    statusIcon = Icons.event_busy;
                    break;
                  case RoomOccupancyStatus.maintenance:
                    statusColor = Colors.red;
                    statusIcon = Icons.build;
                    break;
                }

                return ListTile(
                  title: Text(status.toString().split('.').last),
                  leading: Icon(statusIcon, color: statusColor),
                  selected: _filterByStatus == status,
                  onTap: () {
                    setState(() {
                      _filterByStatus = status;
                    });
                    Navigator.pop(context);
                  },
                );
              }),
            ],
          ),
    );
  }

  // Room type selector bottom sheet
  void _showRoomTypeSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Select Room Type',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
              ),
              const Divider(height: 0),
              Expanded(
                child: ListView(
                  children: [
                    // All types option
                    ListTile(
                      title: const Text('All Types'),
                      leading: const Icon(Icons.category),
                      selected: _filterByRoomType == null,
                      onTap: () {
                        setState(() {
                          _filterByRoomType = null;
                        });
                        Navigator.pop(context);
                      },
                    ),
                    // Individual types
                    ..._availableRoomTypes.map((type) {
                      return ListTile(
                        title: Text(type),
                        leading: const Icon(Icons.category),
                        selected: _filterByRoomType == type,
                        onTap: () {
                          setState(() {
                            _filterByRoomType = type;
                          });
                          Navigator.pop(context);
                        },
                      );
                    }),
                  ],
                ),
              ),
            ],
          ),
    );
  }

  // Sort options selector bottom sheet
  void _showSortOptionSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Sort By',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
              ),
              const Divider(height: 0),
              ListTile(
                title: const Text('Name (A-Z)'),
                leading: const Icon(Icons.sort_by_alpha),
                selected: _sortBy == 'name',
                onTap: () {
                  setState(() {
                    _sortBy = 'name';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('Price (High to Low)'),
                leading: const Icon(Icons.trending_down),
                selected: _sortBy == 'price_high',
                onTap: () {
                  setState(() {
                    _sortBy = 'price_high';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('Price (Low to High)'),
                leading: const Icon(Icons.trending_up),
                selected: _sortBy == 'price_low',
                onTap: () {
                  setState(() {
                    _sortBy = 'price_low';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('Room Type'),
                leading: const Icon(Icons.category),
                selected: _sortBy == 'type',
                onTap: () {
                  setState(() {
                    _sortBy = 'type';
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          ),
    );
  }

  Widget _buildRoomsList() {
    final rooms = filteredRooms;

    if (_rooms.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.meeting_room, size: 80, color: Colors.grey),
            const SizedBox(height: 16),
            Text('No rooms yet', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            const Text(
              'Tap the + button to add a room',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    if (rooms.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              _selectedPropertyId != null
                  ? 'No matching rooms for this property'
                  : 'No matching rooms',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _calculateTotalItemCount(rooms),
        itemBuilder: (context, index) {
          // Calculate if this index refers to a room or a section header
          final indexInfo = _getItemForIndex(rooms, index);

          if (indexInfo.isHeader) {
            // Return a section header
            return _buildSectionHeader(indexInfo.propertyId!);
          } else {
            // Return a room card
            final room = rooms[indexInfo.roomIndex!];
            return _buildRoomCard(room);
          }
        },
      ),
    );
  }

  // Calculate the total count of items (rooms + headers)
  int _calculateTotalItemCount(List<Room> rooms) {
    if (_selectedPropertyId != null || rooms.isEmpty) {
      // No headers when filtering by a single property
      return rooms.length;
    }

    // Count unique properties to determine number of headers
    final uniqueProperties = rooms.map((r) => r.propertyId).toSet();
    return rooms.length + uniqueProperties.length;
  }

  // Get the item type and data for a given index
  _ListItemInfo _getItemForIndex(List<Room> rooms, int index) {
    if (_selectedPropertyId != null) {
      // When filtering by property, no headers, just rooms
      return _ListItemInfo(isHeader: false, roomIndex: index);
    }

    int currentIndex = 0;
    String? currentPropertyId;

    for (int i = 0; i < rooms.length; i++) {
      final room = rooms[i];

      // If property changed, add a header
      if (currentPropertyId != room.propertyId) {
        currentPropertyId = room.propertyId;

        // If this is the header's position, return it
        if (currentIndex == index) {
          return _ListItemInfo(isHeader: true, propertyId: currentPropertyId);
        }

        currentIndex++;
      }

      // If this is a room's position, return it
      if (currentIndex == index) {
        return _ListItemInfo(isHeader: false, roomIndex: i);
      }

      currentIndex++;
    }

    // Fallback - should never reach here if index is valid
    return _ListItemInfo(isHeader: false, roomIndex: index);
  }

  // Build a section header for a property
  Widget _buildSectionHeader(String propertyId) {
    // Get the property name from the ID
    final property = _properties.firstWhere(
      (p) => p.id == propertyId,
      orElse:
          () => Property(
            id: propertyId,
            name: 'Unknown Property',
            address: '',
            city: '',
            state: '',
            zipCode: '',
            createdAt: DateTime.now(),
          ),
    );

    return Padding(
      padding: const EdgeInsets.fromLTRB(4, 16, 4, 8),
      child: Row(
        children: [
          const Icon(Icons.location_city, size: 18),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              property.name,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ),
          Text(
            '${_countRoomsForProperty(propertyId)} rooms',
            style: TextStyle(color: Colors.grey[700], fontSize: 12),
          ),
        ],
      ),
    );
  }

  // Count the number of rooms for a property
  int _countRoomsForProperty(String propertyId) {
    return filteredRooms.where((room) => room.propertyId == propertyId).length;
  }

  Widget _buildRoomCard(Room room) {
    // Get color based on occupancy status
    Color statusColor;
    switch (room.occupancyStatus) {
      case RoomOccupancyStatus.vacant:
        statusColor = Colors.green;
        break;
      case RoomOccupancyStatus.occupied:
        statusColor = Colors.blue;
        break;
      case RoomOccupancyStatus.reserved:
        statusColor = Colors.orange;
        break;
      case RoomOccupancyStatus.maintenance:
        statusColor = Colors.red;
        break;
    }

    return FutureBuilder<Property?>(
      future: serviceLocator.propertyService.getPropertyById(room.propertyId),
      builder: (context, snapshot) {
        final propertyName =
            snapshot.hasData ? snapshot.data!.name : 'Loading...';

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 1,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: InkWell(
            onTap: () => _viewRoomDetails(room),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Room image - reduced to 30% width
                  ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width * 0.25,
                      height: 100,
                      child:
                          room.imageUrl != null && room.imageUrl!.isNotEmpty
                              ? Image.network(
                                room.imageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder:
                                    (context, error, stackTrace) => Container(
                                      color: Colors.grey[300],
                                      child: const Icon(
                                        Icons.meeting_room,
                                        size: 40,
                                      ),
                                    ),
                              )
                              : Container(
                                color: Colors.grey[300],
                                child: const Icon(Icons.meeting_room, size: 40),
                              ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Room details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Room name and status
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                room.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: statusColor.withAlpha(51),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: statusColor),
                              ),
                              child: Text(
                                room.occupancyStatus.toString().split('.').last,
                                style: TextStyle(
                                  color: statusColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 4),

                        // Property name
                        Row(
                          children: [
                            const Icon(
                              Icons.home,
                              size: 14,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                propertyName,
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[700],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 6),

                        // Room type
                        Row(
                          children: [
                            const Icon(
                              Icons.category,
                              size: 14,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              room.roomTypeName,
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 6),

                        // Rental price and furnished status
                        Wrap(
                          spacing: 16,
                          runSpacing: 8,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Rental Price: ',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey[800],
                                  ),
                                ),
                                Text(
                                  CurrencyFormatter.formatAmount(room.rentalPrice),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 13,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Furnished: ',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey[800],
                                  ),
                                ),
                                Text(
                                  room.isFurnished ? 'Yes' : 'No',
                                  style: TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        room.isFurnished
                                            ? Colors.green[700]
                                            : Colors.grey[800],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Amenities as chips
                        if (room.amenities.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children:
                                room.amenities.take(3).map((amenity) {
                                  return Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 3,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.withAlpha(30),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.blue.withAlpha(100),
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      amenity.displayName,
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.blue[700],
                                      ),
                                    ),
                                  );
                                }).toList(),
                          ),
                        ],

                        const SizedBox(height: 8),

                        // Action buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Assign to tenant button (only for vacant rooms)
                            if (room.occupancyStatus == RoomOccupancyStatus.vacant) ...[
                              _buildIconButton(
                                icon: Icons.person_add,
                                onPressed: () => _assignRoomToTenant(room),
                                color: Colors.green,
                              ),
                              const SizedBox(width: 8),
                            ],
                            // Vacate room button (only for occupied rooms)
                            if (room.occupancyStatus == RoomOccupancyStatus.occupied) ...[
                              _buildIconButton(
                                icon: Icons.no_accounts,
                                onPressed: () => _vacateRoom(room),
                                color: Colors.orange,
                              ),
                              const SizedBox(width: 8),
                            ],
                            _buildIconButton(
                              icon: Icons.edit,
                              onPressed: () => _editRoom(room),
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            _buildIconButton(
                              icon: Icons.delete,
                              onPressed: () => _deleteRoom(room),
                              color: Colors.red,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Compact icon button
  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Icon(icon, size: 20, color: color),
      ),
    );
  }

  void _addRoom() async {
    if (_properties.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You need to create a property first'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // If only one property exists, use it directly
    if (_properties.length == 1) {
      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => RoomFormPage(propertyId: _properties[0].id),
        ),
      );

      if (result == true) {
        _loadData();
      }
      return;
    }

    // If multiple properties exist, show dialog to select one
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Property'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _properties.length,
                itemBuilder: (context, index) {
                  final property = _properties[index];
                  return ListTile(
                    title: Text(property.name),
                    subtitle: Text(property.address),
                    onTap: () async {
                      Navigator.pop(context);

                      final result = await Navigator.push<bool>(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  RoomFormPage(propertyId: property.id),
                        ),
                      );

                      if (result == true) {
                        _loadData();
                      }
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
            ],
          ),
    );
  }

  void _editRoom(Room room) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => RoomFormPage(room: room)),
    );

    if (result == true) {
      _loadData();
    }
  }

  void _viewRoomDetails(Room room) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => RoomDetailPage(roomId: room.id)),
    ).then((_) => _loadData());
  }

  void _assignRoomToTenant(Room room) async {
    // Show tenant selection dialog first
    await _showTenantSelectionDialog(room);
  }
  
  void _vacateRoom(Room room) async {
    // Show confirmation dialog first
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Vacate ${room.name}'),
        content: const Text('Are you sure you want to vacate this room? This will change the room status to vacant and update tenant records.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () async {
              // Capture scaffold messenger
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              
              // Close dialog
              Navigator.pop(context);
              
              // Show loading indicator
              setState(() {
                _isLoading = true;
              });
              
              try {
                // 1. Get the tenant assigned to this room
                final tenants = await serviceLocator.tenantService.getAllTenants();
                final tenant = tenants.firstWhere(
                  (t) => t.roomId == room.id,
                  orElse: () => throw Exception('No tenant found for this room'),
                );
                
                // 2. Update tenant record - set roomId to null and status to movedOut
                final updatedTenant = tenant.copyWith(
                  roomId: null,
                  status: TenantStatus.movedOut,
                );
                await serviceLocator.tenantService.updateTenant(updatedTenant);
                
                // 3. Update room status to vacant
                await serviceLocator.roomService.updateRoomOccupancyStatus(
                  room.id, 
                  RoomOccupancyStatus.vacant
                );
                
                // 4. Check if still mounted
                if (!mounted) return;
                
                // Show success message
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('${room.name} has been vacated'),
                    backgroundColor: Colors.green,
                  ),
                );
                
                // 5. Refresh the room list
                _loadData();
              } catch (e) {
                // Check if still mounted
                if (!mounted) return;
                
                // Show error message
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('Error vacating room: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              } finally {
                // Hide loading indicator
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: const Text('VACATE'),
          ),
        ],
      ),
    );
  }
  
  Future<void> _showTenantSelectionDialog(Room room) async {
    // Fetch all tenants and filter for those with pending status
    final allTenants = await serviceLocator.tenantService.getAllTenants();
    final tenants = allTenants.where((tenant) => tenant.status == TenantStatus.pending).toList();
    
    if (tenants.isEmpty) {
      // If no pending tenants, show a message
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No pending tenants available to assign to this room'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    if (!mounted) return;
    
    // Show dialog to select tenant
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Assign ${room.name} to Tenant'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: tenants.length,
            itemBuilder: (context, index) {
              final tenant = tenants[index];
              return ListTile(
                leading: const Icon(Icons.person),
                title: Text('${tenant.firstName} ${tenant.lastName}'),
                subtitle: Text(tenant.email),
                onTap: () async {
                  // Capture the scaffold messenger before any async operation
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  
                  Navigator.pop(context); // Close dialog
                  
                  // Show loading indicator
                  setState(() {
                    _isLoading = true;
                  });
                  
                  try {
                    // 1. Update tenant with the room ID
                    final updatedTenant = tenant.copyWith(
                      roomId: room.id,
                      status: TenantStatus.active,
                    );
                    
                    // 2. Update tenant in database
                    await serviceLocator.tenantService.updateTenant(updatedTenant);
                    
                    // 3. Update room status to occupied
                    await serviceLocator.roomService.updateRoomOccupancyStatus(
                      room.id, 
                      RoomOccupancyStatus.occupied
                    );
                    
                    // 4. Check if still mounted
                    if (!mounted) return;
                    
                    // Show success message (using captured scaffoldMessenger)
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('${room.name} assigned to ${tenant.firstName} ${tenant.lastName}'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    
                    // 5. Refresh the room list
                    _loadData();
                  } catch (e) {
                    // Check if still mounted
                    if (!mounted) return;
                    
                    // Show error message (using captured scaffoldMessenger)
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('Error assigning room: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  } finally {
                    // Hide loading indicator
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                    }
                  }
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }

  void _deleteRoom(Room room) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Room'),
            content: Text('Are you sure you want to delete "${room.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  // Capture ScaffoldMessenger before async gap
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  setState(() {
                    _isLoading = true;
                  });

                  try {
                    final success = await serviceLocator.roomService.deleteRoom(
                      room.id,
                    );

                    if (mounted) {
                      if (success) {
                        _loadData();
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(content: Text('Room deleted')),
                        );
                      } else {
                        setState(() {
                          _isLoading = false;
                        });
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(
                            backgroundColor: Colors.red,
                            content: Text('Failed to delete room'),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          backgroundColor: Colors.red,
                          content: Text('Error: ${e.toString()}'),
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('DELETE'),
              ),
            ],
          ),
    );
  }
}
