import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'l10n/app_localizations.dart';
import 'models/bill/bill.dart';
import 'screens/auth/auth_wrapper.dart';
import 'screens/auth/forgot_password_screen.dart';
import 'screens/bills/add_bill_page.dart';
import 'screens/bills/bill_templates_page.dart';
import 'screens/bills/group_bill_create_page.dart';
import 'screens/bills/select_tenant_page.dart';
import 'screens/dashboard/dashboard_page.dart';
import 'screens/expenses/expense_detail_page.dart';
import 'screens/expenses/expense_form_page.dart';
import 'screens/expenses/expenses_list_page.dart';
import 'screens/payments/payment_history_page.dart';
import 'screens/payments/payment_management_page.dart';
import 'screens/payments/select_tenant_for_payment_page.dart';
import 'screens/profile/profile_page.dart';
import 'screens/properties/properties_list_page.dart';
import 'screens/reports/reports_dashboard_page.dart';
import 'screens/reports/income_summary_report.dart';
import 'screens/reports/rent_collection_report.dart';
import 'screens/reports/expense_tracker_report.dart';
import 'screens/reports/profit_report.dart';
import 'screens/reports/occupancy_report_page.dart';
import 'screens/reports/lease_expiration_report.dart';
import 'screens/rooms/rooms_list_page.dart';
import 'screens/settings/settings_page.dart';
import 'screens/tenants/tenants_list_page.dart';
import 'screens/legal/terms_and_conditions_screen.dart';
import 'screens/legal/privacy_policy_screen.dart';
import 'services/service_locator.dart';
import 'services/bill_generation_service.dart';
import 'services/expense/expense_service.dart';
import 'services/expense/expense_category_service.dart';
import 'services/expense/vendor_service.dart';
import 'services/supabase_service.dart';
import 'utils/logger.dart';
import 'widgets/common/app_error_widget.dart';

// Define a global key for the navigator to handle auth state
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Timer for token refresh checks
Timer? _tokenRefreshTimer;

void main() async {
  // Catch all errors in the main function
  try {
    WidgetsFlutterBinding.ensureInitialized();
    // Initialize logger
    AppLogger.init();

    // Initialize services before starting the app
    try {
      // Check if Supabase is already initialized to avoid duplicate initialization
      bool isSupabaseInitialized = false;
      try {
        // This will throw an exception if Supabase is not initialized
        Supabase.instance.client;
        isSupabaseInitialized = true;
        AppLogger.info('Supabase is already initialized');
      } catch (e) {
        // Supabase is not initialized yet, which is the expected case
        AppLogger.info('Supabase not yet initialized, will initialize now');
      }

      if (!isSupabaseInitialized) {
        // Initialize Supabase client with retry logic
        int retryCount = 0;
        const maxRetries = 3;
        bool initialized = false;

        while (retryCount < maxRetries && !initialized) {
          try {
            await Supabase.initialize(
              url: 'https://qyovaxxljzfpnciumarh.supabase.co',
              anonKey:
                  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF5b3ZheHhsanpmcG5jaXVtYXJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0NTI3NTMsImV4cCI6MjA2NTAyODc1M30.xI1BLoCWWTzMiDJM9jaVNuPwdFosznmACQn1BmAFK_8',
            );
            initialized = true;
            AppLogger.info('Supabase initialized successfully');
          } catch (e) {
            retryCount++;
            AppLogger.error(
              'Supabase initialization failed (attempt $retryCount/$maxRetries): $e',
            );
            if (retryCount < maxRetries) {
              // Wait before retrying
              await Future.delayed(Duration(seconds: 1 * retryCount));
            } else {
              // Rethrow on final attempt
              rethrow;
            }
          }
        }
      }

      // Initialize the rest of the services
      await serviceLocator.initializeWithoutSupabase();
      
      // Explicitly initialize expense services to ensure they're available
      try {
        // Make sure SupabaseService is initialized
        serviceLocator.supabaseService = SupabaseService.instance;
        
        // Initialize expense services
        serviceLocator.expenseService = ExpenseService(serviceLocator.supabaseService);
        serviceLocator.expenseCategoryService = ExpenseCategoryService(serviceLocator.supabaseService);
        serviceLocator.vendorService = VendorService(serviceLocator.supabaseService);
        
        AppLogger.info('Expense services initialized successfully');
      } catch (e) {
        AppLogger.error('Error initializing expense services: $e');
      }

      AppLogger.info('All services initialized successfully');
      
      // Start token refresh timer
      startTokenRefreshTimer();
      
      // Schedule automated bill generation
      BillGenerationService.scheduleDailyCheck();
      AppLogger.info('Automated bill generation scheduled');
    } catch (e) {
      AppLogger.error('Failed to initialize services: $e');
      // Continue with the app, the SplashPage will handle the error
    }

    // Start app
    runApp(const MyApp());
  } catch (e, stackTrace) {
    // Log any uncaught errors
    AppLogger.error('Uncaught error in main: $e\n$stackTrace');

    // Start the app anyway, but with error information
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 80, color: Colors.red),
                  const SizedBox(height: 20),
                  const Text(
                    'Critical Error',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'The application failed to start: ${e.toString()}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      // Try to restart the app
                      main();
                    },
                    child: const Text('Restart App'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Start periodic token refresh check
void startTokenRefreshTimer() {
  // Cancel any existing timer
  _tokenRefreshTimer?.cancel();
  
  // Check token validity every 2 minutes
  _tokenRefreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) async {
    try {
      if (serviceLocator.authService.isLoggedIn) {
        AppLogger.debug('Checking token expiry status');
        await serviceLocator.authService.checkAndRefreshTokenIfNeeded();
      }
    } catch (e) {
      AppLogger.error('Error in token refresh timer: $e');
    }
  });
  
  AppLogger.info('Token refresh timer started');
}

// Stop token refresh timer
void stopTokenRefreshTimer() {
  _tokenRefreshTimer?.cancel();
  _tokenRefreshTimer = null;
  AppLogger.info('Token refresh timer stopped');
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void dispose() {
    // Stop token refresh timer when app is disposed
    stopTokenRefreshTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tenanta',
      navigatorKey: navigatorKey,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const SplashPage(),
      routes: {
        '/auth': (context) => const AuthWrapper(),
        '/login':
            (context) =>
                const AuthWrapper(),
        '/dashboard': (context) => const DashboardPage(),
        '/properties': (context) => const PropertiesListPage(),
        '/rooms': (context) => const RoomsListPage(),
        '/tenants': (context) => const TenantsListPage(),
        '/settings': (context) => const SettingsPage(),
        '/profile': (context) => const ProfilePage(),
        '/auth/reset-password': (context) => const ForgotPasswordScreen(),
        '/payments': (context) => const PaymentManagementPage(),
        '/payment-history': (context) => const PaymentHistoryPage(),
        '/expenses': (context) => const ExpensesListPage(),
        '/expenses/add': (context) => const ExpenseFormPage(),
        '/expenses/edit': (context) {
          final expenseId = ModalRoute.of(context)?.settings.arguments as String?;
          return ExpenseFormPage(expenseId: expenseId);
        },
        '/expenses/detail': (context) {
          final expenseId = ModalRoute.of(context)?.settings.arguments as String;
          return ExpenseDetailPage(expenseId: expenseId);
        },
        '/select-tenant-for-bill':
            (context) => const SelectTenantPage(isForPayment: false),
        '/select-tenant-for-payment':
            (context) => const SelectTenantForPaymentPage(),
        '/multi-tenant-bill': (context) => const GroupBillCreatePage(),
        '/bill-templates': (context) => const BillTemplatesPage(),
        '/edit-bill': (context) {
          final bill = ModalRoute.of(context)?.settings.arguments as Bill?;
          return AddBillPage(bill: bill);
        },
        '/reports': (context) => const ReportsDashboardPage(),
        '/reports/income-summary': (context) => const IncomeSummaryReport(),
        '/reports/rent-collection': (context) => const RentCollectionReport(),
        '/reports/expense-tracker': (context) => const ExpenseTrackerReport(),
        '/reports/profit': (context) => const ProfitReport(),
        '/reports/occupancy': (context) => const OccupancyReportPage(),
        '/reports/lease-expiration': (context) => const LeaseExpirationReport(),
        '/legal/terms': (context) => const TermsAndConditionsScreen(),
        '/legal/privacy': (context) => const PrivacyPolicyScreen(),
      },
      // Add localization support
      supportedLocales: const [
        Locale('en'), // English
        Locale('sw'), // Swahili
      ],
      // These delegates make sure that the localization data is available
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      // Use locale from settings service (default to device locale)
      localeResolutionCallback: (locale, supportedLocales) {
        // Return the user's preferred locale if settingsService is initialized
        try {
          final userLocale = serviceLocator.settingsService.currentLocale;
          return userLocale;
        } catch (e) {
          // If settingsService is not initialized or there's an error,
          // fall back to device locale or English
          if (locale != null) {
            for (var supportedLocale in supportedLocales) {
              if (supportedLocale.languageCode == locale.languageCode) {
                return supportedLocale;
              }
            }
          }
          // Default to English
          return const Locale('en');
        }
      },
    );
  }
}

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => SplashPageState();
}

class SplashPageState extends State<SplashPage> {
  bool _isLoading = true;
  bool _isError = false;
  dynamic _error;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _isError = false;
      _error = null;
      _isRetrying = false;
    });

    try {
      // Check for expired token and refresh it if needed
      if (serviceLocator.authService.isLoggedIn) {
        AppLogger.info('Checking token during app initialization');
        try {
          await serviceLocator.authService.checkAndRefreshTokenIfNeeded();
        } catch (tokenError) {
          AppLogger.error('Error refreshing token during initialization: $tokenError');
          // Continue with session validation even if token refresh fails
        }
      }

      // Enhanced session validation
      final isValidSession = await serviceLocator.authService.validateSession();

      // Add delay for a smoother experience
      await Future.delayed(const Duration(milliseconds: 500));

      // Store navigation actions to be performed after checking mounted state
      String navigationRoute;
      Future<void> Function()? sessionAction;

      if (isValidSession) {
        // If session is valid, rotate it for better security (mitigates session fixation)
        sessionAction = () => serviceLocator.sessionService.rotateSession();
        navigationRoute = '/dashboard';

        // Ensure user profile exists
        try {
          await serviceLocator.userProfileService.ensureProfileExists();
        } catch (profileError) {
          AppLogger.error('Error ensuring profile exists: $profileError');
          // Continue with navigation even if profile creation fails
        }
      } else {
        // Ensure any invalid sessions are cleared
        sessionAction = () => serviceLocator.sessionService.invalidateSession();
        navigationRoute = '/auth';
      }

      // Check if widget is still mounted before performing UI updates
      if (mounted) {
        // Execute session action
        try {
          await sessionAction();
        } catch (sessionError) {
          AppLogger.error('Session action error: $sessionError');
          // Continue with navigation even if session action fails
        }

        // Navigate only if still mounted after session actions
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(navigationRoute);
        }
      }
    } catch (e) {
      AppLogger.error('Error during app initialization: $e');
      if (mounted) {
        setState(() {
          _isError = true;
          _error = e;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/Shadow-Home.png',
              height: 80,
              width: 80,
            ),
            const SizedBox(height: 24),
            const Text(
              'Tenanta',
              style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              Column(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    _isRetrying ? 'Retrying...' : 'Initializing app...',
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              )
            else if (_isError)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: AppErrorWidget.fromException(
                  _error,
                  title: 'Initialization Error',
                  onRetry: () {
                    setState(() {
                      _isRetrying = true;
                    });
                    _initializeApp();
                  },
                  additionalActions: [
                    TextButton(
                      onPressed: () {
                        // Force navigation to auth screen even with errors
                        Navigator.of(context).pushReplacementNamed('/auth');
                      },
                      child: const Text('Go to Login'),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
