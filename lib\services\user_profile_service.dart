import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user/user_profile_model.dart';
import '../utils/logger.dart';

class UserProfileService {
  final SupabaseClient client;

  UserProfileService(this.client);

  // Get a user profile by ID
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      // Try to get from profiles table
      try {
        final response = await client
            .from('profiles')
            .select()
            .eq('id', userId)
            .maybeSingle();
            
        if (response != null) {
          return UserProfile.fromJson(response);
        }
      } catch (e) {
        AppLogger.error('Error getting user profile by ID: $e');
      }
      
      // If we couldn't get the profile, return null
      return null;
    } catch (e) {
      AppLogger.error('Unexpected error in getUserProfile: $e');
      return null;
    }
  }

  // Get the current user's profile
  Future<UserProfile?> getCurrentUserProfile() async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        return null;
      }

      try {
        // First try to get from profiles table
        final response =
            await client.from('profiles').select().eq('id', user.id).single();

        // Check if user_profile exists
        final userProfileExists = await _checkUserProfileExists(user.id);
        if (!userProfileExists) {
          // Create user_profile if it doesn't exist
          final profile = UserProfile.fromJson(response);
          await client.from('user_profiles').insert({
            'id': user.id,
            'role': 'tenant',
            'display_name': profile.fullName ?? 'User ${user.id.substring(0, 8)}',
            'first_name': profile.firstName,
            'last_name': profile.lastName,
          });
        }

        // Return profile from JSON
        return UserProfile.fromJson(response);
      } catch (e) {
        AppLogger.error('Error getting user profile: $e');

        // Specific handling for PostgrestException with code PGRST116 (no rows returned)
        if (e.toString().contains('PGRST116') ||
            e.toString().contains('contains 0 rows')) {
          AppLogger.info(
            'Profile not found (PGRST116), creating new profile for user ${user.id}',
          );

          // Create a new profile from user metadata
          final newProfile = UserProfile.fromUserMetadata(
            user.id,
            user.userMetadata,
            user.email,
          );

          // Insert the new profile into both tables
          try {
            await client.from('profiles').insert(newProfile.toJson());
            await client.from('user_profiles').insert({
              'id': user.id,
              'role': 'tenant',
              'display_name': newProfile.fullName ?? 'User ${user.id.substring(0, 8)}',
              'first_name': newProfile.firstName,
              'last_name': newProfile.lastName,
            });
            AppLogger.info('Created new profiles for user ${user.id}');

            // Return the newly created profile
            return newProfile;
          } catch (insertError) {
            AppLogger.error('Error creating profiles: $insertError');
            // Still return the profile even if we couldn't save it to the database
            return newProfile;
          }
        }

        // For other errors, try to return a profile from user metadata
        return UserProfile.fromUserMetadata(
          user.id,
          user.userMetadata,
          user.email,
        );
      }
    } catch (e) {
      AppLogger.error('Unexpected error in getCurrentUserProfile: $e');
      return null;
    }
  }

  // Ensure profile exists for current user
  Future<bool> ensureProfileExists() async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        return false;
      }

      // Check if profile exists in both tables
      final profileExists = await _checkProfileExists(user.id);
      final userProfileExists = await _checkUserProfileExists(user.id);

      if (!profileExists || !userProfileExists) {
        // Create a new profile
        final newProfile = UserProfile.fromUserMetadata(
          user.id,
          user.userMetadata,
          user.email,
        );

        // Insert into profiles table if needed
        if (!profileExists) {
          await client.from('profiles').insert(newProfile.toJson());
        }

        // Insert into user_profiles table if needed
        if (!userProfileExists) {
          await client.from('user_profiles').insert({
            'id': user.id,
            'role': 'tenant',
            'display_name': newProfile.fullName ?? 'User ${user.id.substring(0, 8)}',
            'first_name': newProfile.firstName,
            'last_name': newProfile.lastName,
          });
        }

        return true;
      }

      return true;
    } catch (e) {
      AppLogger.error('Error ensuring profile exists: $e');
      return false;
    }
  }

  // Check if profile exists in profiles table
  Future<bool> _checkProfileExists(String userId) async {
    try {
      final response =
          await client
              .from('profiles')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      return response != null;
    } catch (e) {
      AppLogger.error('Error checking if profile exists: $e');
      return false;
    }
  }

  // Check if profile exists in user_profiles table
  Future<bool> _checkUserProfileExists(String userId) async {
    try {
      final response =
          await client
              .from('user_profiles')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      return response != null;
    } catch (e) {
      AppLogger.error('Error checking if user profile exists: $e');
      return false;
    }
  }

  // Update user profile
  Future<UserProfile?> updateUserProfile(UserProfile updatedProfile) async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Update user metadata in auth.users
      await client.auth.updateUser(
        UserAttributes(data: updatedProfile.toUserMetadata()),
      );

      // Check if profiles exist in both tables
      final profileExists = await _checkProfileExists(user.id);
      final userProfileExists = await _checkUserProfileExists(user.id);

      if (profileExists) {
        // Update existing profile
        await client
            .from('profiles')
            .update(updatedProfile.toJson())
            .eq('id', user.id);
      } else {
        // Insert new profile
        await client.from('profiles').insert(updatedProfile.toJson());
      }

      // Update or insert user_profile
      final userProfileData = {
        'id': user.id,
        'role': 'tenant',
        'display_name': updatedProfile.fullName ?? 'User ${user.id.substring(0, 8)}',
        'first_name': updatedProfile.firstName,
        'last_name': updatedProfile.lastName,
      };

      if (userProfileExists) {
        await client
            .from('user_profiles')
            .update(userProfileData)
            .eq('id', user.id);
      } else {
        await client.from('user_profiles').insert(userProfileData);
      }

      return await getCurrentUserProfile();
    } catch (e) {
      AppLogger.error('Error updating user profile: $e');
      return null;
    }
  }

  // Update email with OTP verification
  Future<bool> updateEmail(String newEmail) async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Request email change with OTP verification
      await client.auth.updateUser(UserAttributes(email: newEmail));

      return true;
    } catch (e) {
      AppLogger.error('Error updating email: $e');
      return false;
    }
  }

  // Upload profile image
  Future<String?> uploadProfileImage(
    Uint8List imageBytes,
    String fileName,
  ) async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      final String path = 'profile_images/${user.id}/$fileName';

      await client.storage
          .from('user_uploads')
          .uploadBinary(
            path,
            imageBytes,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Get public URL
      final imageUrl = client.storage.from('user_uploads').getPublicUrl(path);

      // Update user metadata with new image URL
      final currentProfile = await getCurrentUserProfile();
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          profileImageUrl: imageUrl,
        );
        await updateUserProfile(updatedProfile);
      }

      return imageUrl;
    } catch (e) {
      AppLogger.error('Error uploading profile image: $e');
      return null;
    }
  }
}
