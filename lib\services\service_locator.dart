import 'package:supabase_flutter/supabase_flutter.dart';
import 'auth_service.dart';
import 'property/property_service.dart';
import 'room/room_service.dart';
import 'amenity/amenity_service.dart';
import 'settings_service.dart';
import 'settings/settings_database_service.dart';
import 'tenant/tenant_service.dart';
import 'activity_log_service.dart';
import 'session_service.dart';
import 'user_profile_service.dart';
import 'bill/bill_service.dart';
import 'bill/bill_tenant_service.dart';
import 'bill/bill_template_service.dart';
import 'payment/payment_service.dart';
import 'supabase_service.dart';
import 'expense/expense_service.dart';
import 'expense/expense_category_service.dart';
import 'expense/vendor_service.dart';
import 'report_service.dart';
import '../utils/logger.dart';

class ServiceLocator {
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  // Services
  late AuthService authService;
  late PropertyService propertyService;
  late RoomService roomService;
  late AmenityService amenityService;
  late SettingsService settingsService;
  late SettingsDatabaseService settingsDatabaseService;
  late TenantService tenantService;
  late ActivityLogService activityLogService;
  late SessionService sessionService;
  late UserProfileService userProfileService;
  late BillService billService;
  late BillTenantService billTenantService;
  late BillTemplateService billTemplateService;
  late PaymentService paymentService;
  late SupabaseService supabaseService;
  late ExpenseService expenseService;
  late ExpenseCategoryService expenseCategoryService;
  late VendorService vendorService;
  late ReportService reportService;

  // Initialize services
  Future<void> initialize() async {
    try {
      // Initialize Supabase client
      await Supabase.initialize(
        url: 'https://qyovaxxljzfpnciumarh.supabase.co',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF5b3ZheHhsanpmcG5jaXVtYXJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0NTI3NTMsImV4cCI6MjA2NTAyODc1M30.xI1BLoCWWTzMiDJM9jaVNuPwdFosznmACQn1BmAFK_8',
      );

      // Initialize other services
      await initializeWithoutSupabase();
    } catch (e) {
      throw Exception('Failed to initialize services: $e');
    }
  }

  // Initialize services without initializing Supabase
  // This is useful when Supabase is already initialized elsewhere
  Future<void> initializeWithoutSupabase() async {
    try {
      final client = Supabase.instance.client;

      // Initialize Supabase service
      supabaseService = SupabaseService.instance;

      // Initialize auth service
      authService = AuthService(client);

      // Initialize session service
      sessionService = SessionService();

      // Initialize property and room services
      propertyService = PropertyService();
      roomService = RoomService();
      amenityService = AmenityService();

      // Initialize tenant service
      tenantService = TenantService(client);

      // Initialize bill service
      billService = BillService();
      
      // Initialize bill-tenant service
      billTenantService = BillTenantService();
      
      // Initialize bill template service
      billTemplateService = BillTemplateService();
      
      // Initialize payment service
      paymentService = PaymentService(billService);

      // Initialize expense services
      expenseService = ExpenseService(supabaseService);
      expenseCategoryService = ExpenseCategoryService(supabaseService);
      vendorService = VendorService(supabaseService);
      
      // Initialize report service
      reportService = ReportService(client);

      // Initialize activity log service
      activityLogService = ActivityLogService(client);

      // Initialize settings database service
      settingsDatabaseService = SettingsDatabaseService(client);

      // Initialize settings service
      settingsService = SettingsService(settingsDatabaseService);
      await settingsService.init();

      // Initialize user profile service
      userProfileService = UserProfileService(client);

      AppLogger.info('Services initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize services: $e');
      throw Exception('Failed to initialize services: $e');
    }
  }
}

// Global instance for easy access
final serviceLocator = ServiceLocator();
