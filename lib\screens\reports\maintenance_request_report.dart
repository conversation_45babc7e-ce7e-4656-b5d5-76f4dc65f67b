import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/ticket/ticket_model.dart';
import '../../services/service_locator.dart';
import '../tickets/ticket_detail_page.dart';
import 'date_range_selector.dart';
import '../../widgets/common/network_error_card.dart';

class MaintenanceRequestReport extends StatefulWidget {
  const MaintenanceRequestReport({super.key});

  @override
  State<MaintenanceRequestReport> createState() => _MaintenanceRequestReportState();
}

class _MaintenanceRequestReportState extends State<MaintenanceRequestReport> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<Ticket> _tickets = [];
  String? _selectedPropertyId;
  List<Map<String, dynamic>> _properties = [];
  late DateTimeRange _selectedDateRange;
  String _selectedPeriod = 'this_month';
  bool _isRetrying = false;
  DateTime? _lastRefreshed;

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: now,
    );
    _loadData();
  }

  Future<void> _loadData() async {
    if (_isRetrying) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
        _isRetrying = true;
      });
    } else {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
    }

    try {
      // Load properties
      final properties = await serviceLocator.propertyService.getAllProperties();
      _properties = properties.map((p) => {
        'id': p.id,
        'name': p.name,
      }).toList();

      // Load tickets with current status
      final tickets = await serviceLocator.ticketService.getUserTickets();

      // Ensure we have the most current status for each ticket
      final updatedTickets = <Ticket>[];
      for (final ticket in tickets) {
        try {
          final currentTicket = await serviceLocator.ticketService.getTicketById(ticket.id);
          updatedTickets.add(currentTicket);
        } catch (e) {
          updatedTickets.add(ticket); // Fallback to original if fetch fails
        }
      }
      
      setState(() {
        _tickets = updatedTickets.where((ticket) {
          final isInDateRange = ticket.createdAt.isAfter(_selectedDateRange.start) &&
              ticket.createdAt.isBefore(_selectedDateRange.end.add(const Duration(days: 1)));

          final matchesProperty = _selectedPropertyId == null ||
              ticket.propertyId == _selectedPropertyId;

          return isInDateRange && matchesProperty;
        }).toList();
        _isLoading = false;
        _isRetrying = false;
        _lastRefreshed = DateTime.now();
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load data: $e';
        _isLoading = false;
        _isRetrying = false;
      });
    }
  }

  void _onDateRangeChanged(DateTimeRange range) {
    setState(() {
      _selectedDateRange = range;
    });
    _loadData();
  }

  void _onPeriodChanged(String period) {
    setState(() {
      _selectedPeriod = period;
    });
  }

  void _onPropertyChanged(String? propertyId) {
    setState(() {
      _selectedPropertyId = propertyId;
    });
    _loadData();
  }

  Map<String, dynamic> _getStatistics() {
    final openTickets = _tickets.where((t) => t.status != TicketStatus.closed).length;
    final resolvedTickets = _tickets.where((t) => t.status == TicketStatus.closed).length;
    final recentlyResolvedTickets = _tickets
        .where((t) => t.status == TicketStatus.closed && 
            t.updatedAt.isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .length;
    
    // Calculate average resolution time for resolved tickets
    final resolvedTicketsList = _tickets.where((t) => t.status == TicketStatus.resolved || t.status == TicketStatus.closed).toList();
    double avgResolutionTime = 0;
    if (resolvedTicketsList.isNotEmpty) {
      final totalResolutionTime = resolvedTicketsList.fold<Duration>(
        Duration.zero,
        (sum, ticket) => sum + (ticket.updatedAt.difference(ticket.createdAt)),
      );
      avgResolutionTime = totalResolutionTime.inHours / resolvedTicketsList.length;
    }

    // Get common issues with priority count
    final categories = _tickets.fold<Map<TicketCategory, Map<String, int>>>(
      {},
      (map, ticket) {
        if (!map.containsKey(ticket.category)) {
          map[ticket.category] = {
            'total': 0,
            'high_priority': 0,
            'recent': 0,
          };
        }
        map[ticket.category]!['total'] = (map[ticket.category]!['total'] ?? 0) + 1;
        
        // Count high priority tickets
        if (ticket.priority == TicketPriority.high || ticket.priority == TicketPriority.urgent) {
          map[ticket.category]!['high_priority'] = (map[ticket.category]!['high_priority'] ?? 0) + 1;
        }
        
        // Count recent tickets (last 7 days)
        if (ticket.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7)))) {
          map[ticket.category]!['recent'] = (map[ticket.category]!['recent'] ?? 0) + 1;
        }
        return map;
      },
    );

    return {
      'openTickets': openTickets,
      'resolvedTickets': resolvedTickets,
      'recentlyResolvedTickets': recentlyResolvedTickets,
      'avgResolutionTime': avgResolutionTime,
      'categories': categories,
    };
  }

  Widget _buildCategoryBreakdown(Map<TicketCategory, Map<String, int>> categories) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Issues by Category',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            const SizedBox(height: 16),
            ...categories.entries.map((entry) {
              final category = entry.key;
              final stats = entry.value;
              final total = stats['total'] ?? 0;
              final highPriority = stats['high_priority'] ?? 0;
              final recent = stats['recent'] ?? 0;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            category.name[0].toUpperCase() + category.name.substring(1),
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            total.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LayoutBuilder(
                      builder: (context, constraints) {
                        return SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                              minWidth: constraints.minWidth,
                              maxWidth: constraints.maxWidth,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (highPriority > 0)
                                  Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade100,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(Icons.priority_high, size: 16, color: Colors.red.shade700),
                                        const SizedBox(width: 4),
                                        Text(
                                          '$highPriority High Priority',
                                          style: TextStyle(
                                            color: Colors.red.shade700,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                if (recent > 0)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade100,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(Icons.new_releases, size: 16, color: Colors.blue.shade700),
                                        const SizedBox(width: 4),
                                        Text(
                                          '$recent New (7d)',
                                          style: TextStyle(
                                            color: Colors.blue.shade700,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final onSurfaceColor = theme.colorScheme.onSurface;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.engineering_outlined,
            size: 64,
            color: Color.fromRGBO(
              (primaryColor.r * 255.0).round() & 0xff,
              (primaryColor.g * 255.0).round() & 0xff,
              (primaryColor.b * 255.0).round() & 0xff,
              0.5,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'No Support Tickets',
            style: theme.textTheme.titleLarge?.copyWith(
              color: Color.fromRGBO(
                (onSurfaceColor.r * 255.0).round() & 0xff,
                (onSurfaceColor.g * 255.0).round() & 0xff,
                (onSurfaceColor.b * 255.0).round() & 0xff,
                0.7,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedPropertyId != null
                ? 'No tickets found for the selected property in this date range'
                : 'No support tickets found for the selected date range',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Color.fromRGBO(
                (onSurfaceColor.r * 255.0).round() & 0xff,
                (onSurfaceColor.g * 255.0).round() & 0xff,
                (onSurfaceColor.b * 255.0).round() & 0xff,
                0.5,
              ),
            ),
          ),
          const SizedBox(height: 24),
          FilledButton.tonal(
            onPressed: () {
              setState(() {
                _selectedPropertyId = null;
                final now = DateTime.now();
                _selectedDateRange = DateTimeRange(
                  start: DateTime(now.year, now.month - 1, 1),
                  end: now,
                );
                _selectedPeriod = 'last_month';
              });
              _loadData();
            },
            child: const Text('View Last Month\'s Tickets'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return NetworkErrorCard(
      message: _errorMessage,
      onRetry: () {
        setState(() {
          _isRetrying = true;
        });
        _loadData();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final stats = _getStatistics();
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Support Ticket Report',
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 2,
        shadowColor: theme.colorScheme.shadow,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Report',
            onPressed: _isLoading ? null : () {
              _loadData();
            },
          ),
        ],
      ),
      body: Container(
        color: theme.colorScheme.surface,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _errorMessage.isNotEmpty
                ? _buildErrorState()
                : RefreshIndicator(
                    onRefresh: _loadData,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Property selector
                          DropdownButtonFormField<String?>(
                            decoration: InputDecoration(
                              labelText: 'Select Property',
                              border: const OutlineInputBorder(),
                              filled: true,
                              fillColor: theme.colorScheme.surfaceContainerHighest,
                            ),
                            value: _selectedPropertyId,
                            items: [
                              const DropdownMenuItem<String?>(
                                value: null,
                                child: Text('All Properties'),
                              ),
                              ..._properties.map((property) => DropdownMenuItem<String?>(
                                value: property['id'],
                                child: Text(property['name']),
                              )),
                            ],
                            onChanged: _onPropertyChanged,
                          ),
                          const SizedBox(height: 16),
                          
                          // Date range selector
                          DateRangeSelector(
                            selectedRange: _selectedDateRange,
                            onRangeSelected: _onDateRangeChanged,
                            selectedPeriod: _selectedPeriod,
                            onPeriodChanged: _onPeriodChanged,
                          ),
                          const SizedBox(height: 16),

                          // Last refreshed indicator
                          if (_lastRefreshed != null)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 16,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Last updated: ${DateFormat('MMM d, yyyy h:mm a').format(_lastRefreshed!)}',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          const SizedBox(height: 24),

                          if (_tickets.isEmpty) ...[
                            SizedBox(
                              height: MediaQuery.of(context).size.height * 0.5,
                              child: _buildEmptyState(),
                            ),
                          ] else ...[
                            // Statistics cards
                            Row(
                              children: [
                                Expanded(
                                  child: _buildStatCard(
                                    'Open Tickets',
                                    stats['openTickets'].toString(),
                                    Icons.pending_actions,
                                    Colors.orange,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildStatCard(
                                    'Recently Resolved (7d)',
                                    stats['recentlyResolvedTickets'].toString(),
                                    Icons.check_circle_outline,
                                    Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildStatCard(
                                    'Avg. Resolution Time',
                                    '${stats['avgResolutionTime'].toStringAsFixed(1)} hours',
                                    Icons.timer,
                                    Colors.blue,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildStatCard(
                                    'Total Resolved',
                                    stats['resolvedTickets'].toString(),
                                    Icons.done_all,
                                    Colors.purple,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Categories breakdown
                            if ((stats['categories'] as Map<TicketCategory, Map<String, int>>).isNotEmpty)
                              _buildCategoryBreakdown(stats['categories'] as Map<TicketCategory, Map<String, int>>),
                            
                            const SizedBox(height: 24),

                            // Recent tickets
                            Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: const Text(
                                            'Recent Support Tickets',
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        TextButton.icon(
                                          onPressed: () {
                                            // TODO: Implement view all functionality
                                          },
                                          icon: const Icon(Icons.list),
                                          label: const Text('View All'),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: const NeverScrollableScrollPhysics(),
                                      itemCount: _tickets.length,
                                      itemBuilder: (context, index) {
                                        final ticket = _tickets[index];
                                        final isRecent = ticket.createdAt.isAfter(
                                          DateTime.now().subtract(const Duration(days: 7))
                                        );
                                        
                                        return ListTile(
                                          title: Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  ticket.title,
                                                  overflow: TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                ),
                                              ),
                                              if (isRecent)
                                                Container(
                                                  margin: const EdgeInsets.only(left: 8),
                                                  padding: const EdgeInsets.symmetric(
                                                    horizontal: 6,
                                                    vertical: 2,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: Colors.blue.shade100,
                                                    borderRadius: BorderRadius.circular(12),
                                                  ),
                                                  child: Text(
                                                    'New',
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.blue.shade700,
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          subtitle: Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  '${ticket.getStatusDisplay()} - ${DateFormat('MMM d, yyyy').format(ticket.createdAt)}',
                                                  overflow: TextOverflow.ellipsis,
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ],
                                          ),
                                          leading: Icon(
                                            _getTicketStatusIcon(ticket.status),
                                            color: ticket.getStatusColor(),
                                          ),
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) => TicketDetailPage(
                                                  ticketId: ticket.id,
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTicketStatusIcon(TicketStatus status) {
    switch (status) {
      case TicketStatus.open:
        return Icons.fiber_new;
      case TicketStatus.inProgress:
        return Icons.pending;
      case TicketStatus.resolved:
      case TicketStatus.closed:
        return Icons.check_circle;
    }
  }
} 