import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../widgets/navigation/app_drawer.dart';
import '../../services/service_locator.dart';
import '../../services/settings_service.dart';

class OccupancyReportPage extends StatefulWidget {
  const OccupancyReportPage({super.key});

  @override
  State<OccupancyReportPage> createState() => _OccupancyReportPageState();
}

class _OccupancyReportPageState extends State<OccupancyReportPage> {
  bool _isLoading = true;
  String? _error;
  List<Map<String, dynamic>> _properties = [];
  DateTime _fromDate = DateTime.now().subtract(const Duration(days: 30)); // Default to last 30 days
  DateTime _toDate = DateTime.now();
  String _searchQuery = '';
  late SettingsService _settingsService;
  
  // Filter options
  bool _showTrend = false;
  bool _showForecast = false;
  bool _showVacancyCost = false;
  bool _showRetention = false;
  List<Map<String, dynamic>> _occupancyTrend = [];
  List<Map<String, dynamic>> _occupancyForecast = [];
  List<Map<String, dynamic>> _vacancyCostAnalysis = [];
  List<Map<String, dynamic>> _tenantRetentionData = [];

  // Pagination variables
  int _currentPage = 1;
  final int _itemsPerPage = 10;

  @override
  void initState() {
    super.initState();
    _settingsService = serviceLocator.settingsService;
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final client = Supabase.instance.client;
      
      // Get properties with their room counts and tenant info
      final response = await client
          .from('properties')
          .select('''
            id,
            name,
            created_at,
            rooms!inner (
              id,
              name,
              rental_price,
              occupancy_status,
              room_type:room_types(name),
              tenants (
                id,
                first_name,
                last_name,
                lease_start_date,
                lease_end_date,
                status
              )
            )
          ''');

      if (response.isEmpty) {
        throw Exception('Failed to get occupancy data');
      }

      // Process the data to calculate occupancy stats for the selected date range
      final List<Map<String, dynamic>> processedData = [];
      final List<Map<String, dynamic>> trendData = [];
      final List<Map<String, dynamic>> forecastData = [];
      final List<Map<String, dynamic>> vacancyCostData = [];
      final List<Map<String, dynamic>> retentionData = [];
      
      // Start and end dates for calculations
      final startDate = DateTime(_fromDate.year, _fromDate.month, _fromDate.day);
      final endDate = DateTime(_toDate.year, _toDate.month, _toDate.day, 23, 59, 59);
      
      // Previous 6 months for trend data
      final months = List.generate(6, (index) {
        final month = DateTime(_toDate.year, _toDate.month - index, 1);
        return {
          'date': month,
          'label': DateFormat('MMM').format(month),
        };
      }).reversed.toList();

      // Next 3 months for forecast
      final forecastMonths = List.generate(3, (index) {
        final month = DateTime(_toDate.year, _toDate.month + index + 1, 1);
        return {
          'date': month,
          'label': DateFormat('MMM').format(month),
        };
      });
      
      for (final property in response as List) {
        // Handle potentially null created_at field
        final DateTime? propertyCreatedAt = property['created_at'] != null 
            ? DateTime.tryParse(property['created_at']) 
            : null;
            
        final rooms = List<Map<String, dynamic>>.from(property['rooms']);
        final totalUnits = rooms.length;
        
        // Calculate occupancy for selected date range based on tenant lease dates
        int occupiedUnitsInRange = 0;
        double totalRentalValue = 0;
        int totalLeaseDays = 0;
        final daysInRange = endDate.difference(startDate).inDays + 1;
        
        for (final room in rooms) {
          final tenants = List<Map<String, dynamic>>.from(room['tenants'] ?? []);
          final rentalPrice = double.tryParse(room['rental_price'].toString()) ?? 0.0;
          totalRentalValue += rentalPrice;
          
          // Check if any tenant was active during the selected date range
          final wasOccupied = tenants.any((tenant) {
            final leaseStart = tenant['lease_start_date'] != null 
                ? DateTime.parse(tenant['lease_start_date']) 
                : null;
            final leaseEnd = tenant['lease_end_date'] != null 
                ? DateTime.parse(tenant['lease_end_date']) 
                : null;
                
            if (tenant['status'] == 'active' && 
                (leaseStart == null || leaseStart.isBefore(endDate)) && 
                (leaseEnd == null || leaseEnd.isAfter(startDate))) {
              
              // Calculate lease days for retention analysis
              final effectiveStart = leaseStart?.isBefore(startDate) ?? false 
                  ? startDate 
                  : leaseStart ?? startDate;
              final effectiveEnd = leaseEnd?.isBefore(endDate) ?? false
                  ? leaseEnd!
                  : endDate;
              
              totalLeaseDays += effectiveEnd.difference(effectiveStart).inDays + 1;
              return true;
            }
            return false;
          });
          
          if (wasOccupied || room['occupancy_status'] == 'occupied') {
            occupiedUnitsInRange++;
          }
        }
        
        final vacantUnits = totalUnits - occupiedUnitsInRange;
        final occupancyRate = totalUnits > 0 ? (occupiedUnitsInRange / totalUnits * 100) : 0.0;
        
        // Calculate vacancy cost for the entire date range
        final averageRentalPrice = totalUnits > 0 ? totalRentalValue / totalUnits : 0;
        final vacancyCost = (vacantUnits * averageRentalPrice * daysInRange / 30); // Normalized to monthly cost
        
        // Calculate retention rate
        final retentionRate = daysInRange > 0 
            ? (totalLeaseDays / (totalUnits * daysInRange) * 100) 
            : 0.0;

        processedData.add({
          'property_id': property['id'],
          'property_name': property['name'],
          'total_units': totalUnits,
          'occupied_units': occupiedUnitsInRange,
          'vacant_units': vacantUnits,
          'occupancy_rate': occupancyRate,
          'rooms': rooms,
          'created_at': propertyCreatedAt ?? DateTime.now(),
          'vacancy_cost': vacancyCost,
          'retention_rate': retentionRate,
        });
        
        // Calculate trend data for each month
        final propertyTrend = [];
        for (final month in months) {
          final monthDate = month['date'] as DateTime;
          
          // Skip months before property was created
          if (propertyCreatedAt != null && monthDate.isBefore(propertyCreatedAt)) {
            propertyTrend.add({
              'month': month['label'],
              'rate': null,
            });
            continue;
          }
          
          final startOfTrendMonth = DateTime(monthDate.year, monthDate.month, 1);
          final endOfTrendMonth = DateTime(monthDate.year, monthDate.month + 1, 0);
          
          int occupiedInMonth = 0;
          for (final room in rooms) {
            final tenants = List<Map<String, dynamic>>.from(room['tenants'] ?? []);
            final wasOccupied = tenants.any((tenant) {
              final leaseStart = tenant['lease_start_date'] != null 
                  ? DateTime.parse(tenant['lease_start_date']) 
                  : null;
              final leaseEnd = tenant['lease_end_date'] != null 
                  ? DateTime.parse(tenant['lease_end_date']) 
                  : null;
                  
              return tenant['status'] == 'active' && 
                     (leaseStart == null || leaseStart.isBefore(endOfTrendMonth)) && 
                     (leaseEnd == null || leaseEnd.isAfter(startOfTrendMonth));
            });
            
            if (wasOccupied || room['occupancy_status'] == 'occupied') {
              occupiedInMonth++;
            }
          }
          
          final monthRate = totalUnits > 0 ? (occupiedInMonth / totalUnits * 100) : 0.0;
          propertyTrend.add({
            'month': month['label'],
            'rate': monthRate,
          });
        }
        
        trendData.add({
          'property_id': property['id'],
          'property_name': property['name'],
          'trend': propertyTrend,
        });

        // Calculate forecast data
        final propertyForecast = [];
        
        for (final month in forecastMonths) {
          final forecastDate = month['date'] as DateTime;
          final startOfForecastMonth = DateTime(forecastDate.year, forecastDate.month, 1);
          final endOfForecastMonth = DateTime(forecastDate.year, forecastDate.month + 1, 0);
          
          int projectedOccupiedUnits = 0;
          int confirmedVacancies = 0;
          int potentialVacancies = 0;
          
          for (final room in rooms) {
            final tenants = List<Map<String, dynamic>>.from(room['tenants'] ?? []);
            final currentStatus = room['occupancy_status'] as String?;
            
            // Check existing tenants and their lease status
            bool willBeOccupied = false;
            bool potentialVacancy = false;
            
            // If there are active tenants, check their lease dates
            for (final tenant in tenants) {
              if (tenant['status'] != 'active') continue;
              
              final leaseStart = tenant['lease_start_date'] != null 
                  ? DateTime.parse(tenant['lease_start_date']) 
                  : null;
              final leaseEnd = tenant['lease_end_date'] != null 
                  ? DateTime.parse(tenant['lease_end_date']) 
                  : null;
              
              // Room will be occupied if lease covers the forecast month
              if ((leaseStart == null || leaseStart.isBefore(endOfForecastMonth)) && 
                  (leaseEnd == null || leaseEnd.isAfter(startOfForecastMonth))) {
                willBeOccupied = true;
              }
              
              // Mark as potential vacancy if lease ends within this forecast month
              if (leaseEnd != null && 
                  leaseEnd.isAfter(startOfForecastMonth) && 
                  leaseEnd.isBefore(endOfForecastMonth)) {
                potentialVacancy = true;
              }
            }
            
            // If the room is currently occupied and has no active tenant with a lease,
            // assume it will remain occupied unless there's evidence otherwise
            if (currentStatus == 'occupied' && tenants.isEmpty) {
              willBeOccupied = true;
            }
            
            // Determine the projected status for this room
            if (willBeOccupied) {
              projectedOccupiedUnits++;
              
              // If there's a potential vacancy, count it but keep the room as occupied
              // since we're already counting it in projectedOccupiedUnits
              if (potentialVacancy) {
                potentialVacancies++;
              }
            } else if (currentStatus == 'vacant') {
              confirmedVacancies++;
            } else if (potentialVacancy) {
              // Room will become vacant during this month
              potentialVacancies++;
            }
          }
          
          // Calculate projected occupancy rate:
          // 1. Currently occupied rooms with valid leases
          // 2. Subtract potential vacancies with a 50% probability
          final vacancyAdjustment = (potentialVacancies * 0.5).round(); // Assume 50% of potential vacancies become vacant
          final projectedTotal = projectedOccupiedUnits - vacancyAdjustment;
          
          final forecastRate = totalUnits > 0 
              ? (projectedTotal / totalUnits * 100).clamp(0.0, 100.0)
              : 0.0;
          
          propertyForecast.add({
            'month': month['label'],
            'rate': forecastRate,
            'confirmed_vacancies': confirmedVacancies,
            'potential_vacancies': potentialVacancies,
            'projected_occupied': projectedTotal,
          });
        }
        
        forecastData.add({
          'property_id': property['id'],
          'property_name': property['name'],
          'forecast': propertyForecast,
        });

        // Add vacancy cost analysis
        vacancyCostData.add({
          'property_id': property['id'],
          'property_name': property['name'],
          'monthly_cost': vacancyCost,
          'vacant_units': vacantUnits,
          'average_rental': averageRentalPrice,
        });

        // Add retention data
        retentionData.add({
          'property_id': property['id'],
          'property_name': property['name'],
          'retention_rate': retentionRate,
          'total_lease_days': totalLeaseDays,
          'total_possible_days': totalUnits * daysInRange,
        });
      }

      setState(() {
        _properties = processedData;
        _occupancyTrend = trendData;
        _occupancyForecast = forecastData;
        _vacancyCostAnalysis = vacancyCostData;
        _tenantRetentionData = retentionData;
        _currentPage = 1; // Reset pagination when data is loaded
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load occupancy data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: _fromDate,
        end: _toDate,
      ),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
      await _loadData(); // Reload data for the new date range
    }
  }

  List<Map<String, dynamic>> get _filteredProperties {
    if (_searchQuery.isEmpty) return _properties;

    return _properties.where((property) {
      return property['property_name']
          .toString()
          .toLowerCase()
          .contains(_searchQuery.toLowerCase());
    }).toList();
  }

  List<Map<String, dynamic>> get _paginatedProperties {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;

    if (startIndex >= _filteredProperties.length) return [];

    return _filteredProperties.sublist(
      startIndex,
      endIndex > _filteredProperties.length ? _filteredProperties.length : endIndex,
    );
  }

  int get _totalPages {
    return (_filteredProperties.length / _itemsPerPage).ceil();
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages) {
      setState(() {
        _currentPage = page;
      });
    }
  }

  String _formatDateRange() {
    final DateFormat formatter = DateFormat('MMM d, y');
    return '${formatter.format(_fromDate)} - ${formatter.format(_toDate)}';
  }

  void _previousPeriod() {
    final duration = _toDate.difference(_fromDate);
    setState(() {
      _toDate = _fromDate;
      _fromDate = _fromDate.subtract(duration);
      _loadData();
    });
  }

  void _nextPeriod() {
    final duration = _toDate.difference(_fromDate);
    final now = DateTime.now();
    setState(() {
      _fromDate = _toDate;
      _toDate = _toDate.add(duration);
      if (_toDate.isAfter(now)) {
        _toDate = now;
      }
      _loadData();
    });
  }

  Color _getOccupancyColor(double rate) {
    if (rate >= 80) return Colors.green.shade700;
    if (rate >= 60) return Colors.orange.shade700;
    return Colors.red.shade700;
  }

  void _showRoomDetails(BuildContext context, Map<String, dynamic> property) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      'Rooms in ${property['property_name']}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      shape: const CircleBorder(),
                    ),
                  ),
                ],
              ),
              const Divider(height: 24),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.6,
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: (property['rooms'] as List).length,
                  itemBuilder: (context, index) {
                    final room = (property['rooms'] as List)[index];
                    final tenants = (room['tenants'] as List?)?.where((t) => t['status'] == 'active').toList() ?? [];
                    
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: BorderSide(
                          color: room['occupancy_status'] == 'occupied' 
                            ? Colors.green.withAlpha(100)
                            : Colors.red.withAlpha(100),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  room['name'],
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: room['occupancy_status'] == 'occupied' 
                                      ? Colors.green.withAlpha(50)
                                      : Colors.red.withAlpha(50),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    room['occupancy_status'].toUpperCase(),
                                    style: TextStyle(
                                      color: room['occupancy_status'] == 'occupied' 
                                        ? Colors.green.shade700
                                        : Colors.red.shade700,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: _InfoItem(
                                    label: 'Type',
                                    value: room['room_type']?['name'] ?? 'N/A',
                                    icon: Icons.category,
                                  ),
                                ),
                                Expanded(
                                  child: _InfoItem(
                                    label: 'Rent',
                                    value: _settingsService.formatCurrency(
                                      double.tryParse(room['rental_price'].toString()) ?? 0.0
                                    ),
                                    icon: Icons.attach_money,
                                  ),
                                ),
                              ],
                            ),
                            if (tenants.isNotEmpty) ...[
                              const Divider(height: 24),
                              _InfoItem(
                                label: 'Current Tenant',
                                value: '${tenants.first['first_name']} ${tenants.first['last_name']}',
                                icon: Icons.person,
                              ),
                              if (tenants.first['lease_start_date'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: _InfoItem(
                                    label: 'Lease Period',
                                    value: '${_formatDate(tenants.first['lease_start_date'])} - ${tenants.first['lease_end_date'] != null ? _formatDate(tenants.first['lease_end_date']) : 'Ongoing'}',
                                    icon: Icons.calendar_today,
                                  ),
                                ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('MMM d, y').format(date);
    } catch (e) {
      return 'N/A';
    }
  }

  Widget _buildOverallStats() {
    if (_filteredProperties.isEmpty) return const SizedBox.shrink();

    final totalUnits = _filteredProperties.fold<int>(
      0,
      (sum, p) => sum + (p['total_units'] as int),
    );
    final totalOccupied = _filteredProperties.fold<int>(
      0,
      (sum, p) => sum + (p['occupied_units'] as int),
    );
    final overallRate = totalUnits > 0 ? (totalOccupied / totalUnits * 100) : 0.0;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha(40),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.analytics_outlined, 
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Overall Occupancy',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: 'Occupancy Rate',
                    value: '${overallRate.toStringAsFixed(1)}%',
                    icon: Icons.percent,
                    color: _getOccupancyColor(overallRate),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _StatCard(
                    title: 'Total Units',
                    value: totalUnits.toString(),
                    icon: Icons.home_work,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _StatCard(
                    title: 'Vacant Units',
                    value: (totalUnits - totalOccupied).toString(),
                    icon: Icons.door_front_door,
                    color: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: LinearProgressIndicator(
                value: overallRate / 100,
                backgroundColor: Colors.grey[200],
                color: _getOccupancyColor(overallRate),
                minHeight: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendChart(String propertyId) {
    // Find the property in the trend data or return an empty widget if not found
    final propertyTrend = _occupancyTrend.where((p) => p['property_id'] == propertyId).toList();
    
    // If no trend data found for this property, return an empty widget
    if (propertyTrend.isEmpty) {
      return const SizedBox.shrink();
    }
    
    final trendData = propertyTrend.first['trend'] as List;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            '6-Month Occupancy Trend',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(
          height: 120,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                for (int i = 0; i < trendData.length; i++)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (trendData[i]['rate'] != null) ...[
                            FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                '${(trendData[i]['rate'] as double).toStringAsFixed(0)}%',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              height: 60 * (trendData[i]['rate'] as double) / 100,
                              decoration: BoxDecoration(
                                color: _getOccupancyColor(trendData[i]['rate']),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ] else ...[
                            const Text('N/A', style: TextStyle(fontSize: 10)),
                            const SizedBox(height: 4),
                            Container(
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Center(
                                child: Icon(
                                  Icons.block,
                                  size: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                          const SizedBox(height: 4),
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              trendData[i]['month'],
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPropertyList() {
    final paginatedProperties = _paginatedProperties;

    return Column(
      children: [
        // Scrollable property list
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.6, // 60% of screen height
          child: ListView.builder(
            itemCount: paginatedProperties.length,
            itemBuilder: (context, index) {
              final property = paginatedProperties[index];
          final occupancyRate = property['occupancy_rate'] as double;
          final propertyId = property['property_id'] as String;
          final propertyCreatedAt = property['created_at'] as DateTime;

          return Card(
            elevation: 3,
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: _getOccupancyColor(occupancyRate).withAlpha(50),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () => _showRoomDetails(context, property),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.apartment,
                                    color: _getOccupancyColor(occupancyRate),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      property['property_name'] as String,
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: _getOccupancyColor(occupancyRate).withAlpha(50),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  '${occupancyRate.toStringAsFixed(1)}% Occupied',
                                  style: TextStyle(
                                    color: _getOccupancyColor(occupancyRate),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: _MiniStatCard(
                                label: 'Total Units',
                                value: property['total_units'].toString(),
                              ),
                            ),
                            Expanded(
                              child: _MiniStatCard(
                                label: 'Occupied',
                                value: property['occupied_units'].toString(),
                                color: Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _MiniStatCard(
                                label: 'Vacant',
                                value: property['vacant_units'].toString(),
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: LinearProgressIndicator(
                            value: occupancyRate / 100,
                            backgroundColor: Colors.grey[200],
                            color: _getOccupancyColor(occupancyRate),
                            minHeight: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Center(
                    child: TextButton.icon(
                      onPressed: () => _showRoomDetails(context, property),
                      icon: const Icon(Icons.meeting_room),
                      label: const Text('View Rooms'),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                    ),
                  ),
                ),
                if (_showTrend && _occupancyTrend.any((p) => p['property_id'] == propertyId)) ...[
                  if (_fromDate.isBefore(propertyCreatedAt))
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.orange.shade700, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Property was created on ${DateFormat('MMM d, yyyy').format(propertyCreatedAt)}. Data before this date is not available.',
                              style: TextStyle(
                                color: Colors.orange.shade900,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    _buildTrendChart(propertyId),
                ],
                if (_showForecast && _occupancyForecast.any((p) => p['property_id'] == propertyId)) 
                  _buildForecastChart(_occupancyForecast.where((p) => p['property_id'] == propertyId).toList()),
              ],
            ),
          );
        },
      ),
        ),
        // Pagination widget
        if (_totalPages > 1) _buildPaginationWidget(),
      ],
    );
  }

  Widget _buildPaginationWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          ElevatedButton.icon(
            onPressed: _currentPage > 1 ? () => _goToPage(_currentPage - 1) : null,
            icon: const Icon(Icons.chevron_left),
            label: const Text('Previous'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),

          // Page info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Property ${(_currentPage - 1) * _itemsPerPage + 1}-${(_currentPage - 1) * _itemsPerPage + _paginatedProperties.length} of ${_filteredProperties.length}, Page $_currentPage',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Next button
          ElevatedButton.icon(
            onPressed: _currentPage < _totalPages ? () => _goToPage(_currentPage + 1) : null,
            icon: const Icon(Icons.chevron_right),
            label: const Text('Next'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Occupancy Report'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _selectDateRange(context),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.visibility),
            tooltip: 'Show/Hide Sections',
            onSelected: (value) {
              setState(() {
                switch (value) {
                  case 'trend':
                    _showTrend = !_showTrend;
                    break;
                  case 'forecast':
                    _showForecast = !_showForecast;
                    break;
                  case 'vacancy':
                    _showVacancyCost = !_showVacancyCost;
                    break;
                  case 'retention':
                    _showRetention = !_showRetention;
                    break;
                }
              });
            },
            itemBuilder: (context) => [
              CheckedPopupMenuItem(
                checked: _showTrend,
                value: 'trend',
                child: const Text('6-Month Trend'),
              ),
              CheckedPopupMenuItem(
                checked: _showForecast,
                value: 'forecast',
                child: const Text('Forecast'),
              ),
              CheckedPopupMenuItem(
                checked: _showVacancyCost,
                value: 'vacancy',
                child: const Text('Vacancy Cost'),
              ),
              CheckedPopupMenuItem(
                checked: _showRetention,
                value: 'retention',
                child: const Text('Retention Analysis'),
              ),
            ],
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withAlpha(25),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.chevron_left),
                          onPressed: _previousPeriod,
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () => _selectDateRange(context),
                            child: Text(
                              _formatDateRange(),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.chevron_right),
                          onPressed: _toDate.isBefore(DateTime.now()) ? _nextPeriod : null,
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                          _currentPage = 1; // Reset to first page when searching
                        });
                      },
                      decoration: InputDecoration(
                        hintText: 'Search properties...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ],
                ),
              ),
              if (_isLoading)
                const Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Center(child: CircularProgressIndicator()),
                )
              else if (_error != null)
                Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          _error!, 
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadData,
                          child: const Text('Try Again'),
                        ),
                      ],
                    ),
                  ),
                )
              else if (_properties.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.home_work, color: Colors.grey, size: 48),
                        SizedBox(height: 16),
                        Text('No properties found for this month'),
                      ],
                    ),
                  ),
                )
              else if (_filteredProperties.isEmpty)
                Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.search_off, color: Colors.grey, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'No properties found matching "${_searchQuery}"',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                  ),
                )
              else ...[
                _buildOverallStats(),
                if (_showVacancyCost)
                  _buildVacancyCostAnalysis(),
                if (_showRetention)
                  _buildRetentionAnalysis(),
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Text(
                    'Property Breakdown',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildPropertyList(),
                const SizedBox(height: 16),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVacancyCostAnalysis() {
    if (_vacancyCostAnalysis.isEmpty) return const SizedBox.shrink();

    final totalVacancyCost = _vacancyCostAnalysis.fold<double>(
      0,
      (sum, p) => sum + (p['monthly_cost'] as double),
    );

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.money_off, 
                    color: Colors.orange.shade700,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Vacancy Cost Analysis',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: 'Total Monthly Loss',
                    value: _settingsService.formatCurrency(totalVacancyCost),
                    icon: Icons.trending_down,
                    color: Colors.red.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _vacancyCostAnalysis.length,
              itemBuilder: (context, index) {
                final property = _vacancyCostAnalysis[index];
                return ListTile(
                  title: Text(property['property_name']),
                  subtitle: Text(
                    '${property['vacant_units']} vacant units @ ${_settingsService.formatCurrency(property['average_rental'])} avg. rent',
                  ),
                  trailing: Text(
                    _settingsService.formatCurrency(property['monthly_cost']),
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetentionAnalysis() {
    if (_tenantRetentionData.isEmpty) return const SizedBox.shrink();

    final averageRetention = _tenantRetentionData.fold<double>(
      0,
      (sum, p) => sum + (p['retention_rate'] as double),
    ) / _tenantRetentionData.length;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.people_outline, 
                    color: Colors.blue.shade700,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Tenant Retention Analysis',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    title: 'Average Retention Rate',
                    value: '${averageRetention.toStringAsFixed(1)}%',
                    icon: Icons.timeline,
                    color: _getRetentionColor(averageRetention),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _tenantRetentionData.length,
              itemBuilder: (context, index) {
                final property = _tenantRetentionData[index];
                final retentionRate = property['retention_rate'] as double;
                return ListTile(
                  title: Text(property['property_name']),
                  subtitle: Text(
                    'Occupied days: ${property['total_lease_days']} / ${property['total_possible_days']}',
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getRetentionColor(retentionRate).withAlpha(50),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${retentionRate.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _getRetentionColor(retentionRate),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Color _getRetentionColor(double rate) {
    if (rate >= 90) return Colors.green.shade700;
    if (rate >= 75) return Colors.orange.shade700;
    return Colors.red.shade700;
  }

  Widget _buildForecastChart(List<Map<String, dynamic>> forecastData) {
    // Find the forecast data for the current property
    if (forecastData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // Extract the forecast array from the first property
    // This needs to be fixed to use the correct property's forecast data
    final propertyForecast = forecastData.first['forecast'] as List;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                '3-Month Forecast',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Tooltip(
                message: 'Forecast based on current occupancy, lease end dates, and historical trends',
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Container(
          height: 175, 
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              for (int i = 0; i < propertyForecast.length; i++)
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            '${((propertyForecast[i]['rate'] as num?)?.toDouble() ?? 0.0).toStringAsFixed(0)}%',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _getForecastColor(i),
                            ),
                          ),
                        ),
                        const SizedBox(height: 2), 
                        Stack(
                          children: [
                            Container(
                              height: 100,
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            Container(
                              height: 100 * ((propertyForecast[i]['rate'] as num?)?.toDouble() ?? 0.0) / 100,
                              decoration: BoxDecoration(
                                color: _getForecastColor(i).withAlpha(180),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: _getForecastColor(i),
                                  width: 1.5,
                                ),
                              ),
                            ),
                            if (((propertyForecast[i]['potential_vacancies'] as int?) ?? 0) > 0)
                              Positioned(
                                right: 0,
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '${propertyForecast[i]['potential_vacancies']}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 2), // Reduced from 4 to 2
                        FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            propertyForecast[i]['month'] ?? '',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w500,
                              color: _getForecastColor(i),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                'Potential Vacancies',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getForecastColor(int index) {
    final colors = [
      const Color(0xFF6366F1), // Indigo
      const Color(0xFF0EA5E9), // Sky blue
      const Color(0xFF10B981), // Emerald
    ];
    return colors[index % colors.length];
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData? icon;
  final Color? color;

  const _StatCard({
    required this.title,
    required this.value,
    this.icon,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color != null ? color!.withAlpha(25) : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color != null ? color!.withAlpha(75) : Colors.grey[300]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 16, color: color ?? Colors.grey[600]),
                const SizedBox(width: 4),
              ],
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _MiniStatCard extends StatelessWidget {
  final String label;
  final String value;
  final Color? color;

  const _MiniStatCard({
    required this.label,
    required this.value,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
} 

class _InfoItem extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;

  const _InfoItem({
    required this.label,
    required this.value,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (icon != null) ...[
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
        ],
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
} 


