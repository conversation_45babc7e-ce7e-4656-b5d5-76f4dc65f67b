import 'package:flutter/material.dart';
import '../../models/tenant/tenant.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';
import 'add_tenant_page.dart';
import 'tenant_details_page.dart';
import 'tenant_room_info_page.dart';

class TenantsListPage extends StatefulWidget {
  const TenantsListPage({super.key});

  @override
  State<TenantsListPage> createState() => _TenantsListPageState();
}

class _TenantsListPageState extends State<TenantsListPage> {
  List<Tenant>? _tenants;
  List<Property> _properties = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _searchQuery = '';
  String _statusFilter = 'all';
  String? _propertyFilter;
  String _sortBy = 'name';
  bool _sortAscending = true;
  final TextEditingController _searchController = TextEditingController();
  
  // Pagination
  static const int _itemsPerPage = 12;
  int _currentPage = 1;
  
  // Cache for tenant-room-property mapping
  final Map<String, String> _tenantPropertyNames = {};

  @override
  void initState() {
    super.initState();
    _loadTenants();
    _loadProperties();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTenants() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final tenants = await serviceLocator.tenantService.getAllTenants();
      
      // Load property names for tenants with rooms
      for (final tenant in tenants) {
        if (tenant.roomId != null) {
          try {
            final room = await serviceLocator.roomService.getRoomById(tenant.roomId!);
            if (room != null) {
              final property = await serviceLocator.propertyService.getPropertyById(room.propertyId);
              if (property != null) {
                _tenantPropertyNames[tenant.id] = property.name;
              }
            }
          } catch (e) {
            // Handle error silently
          }
        }
      }

      if (!mounted) return;

      setState(() {
        _tenants = tenants;
        _isLoading = false;
        _currentPage = 1;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _errorMessage = 'Failed to load tenants: $e';
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading tenants: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _loadTenants,
            textColor: Colors.white,
          ),
        ),
      );
    }
  }

  Future<void> _loadProperties() async {
    try {
      final properties = await serviceLocator.propertyService.getAllProperties();
      setState(() {
        _properties = properties;
      });
    } catch (e) {
      // Handle error silently for properties
    }
  }

  List<Tenant> get _filteredTenants {
    if (_tenants == null) {
      return [];
    }

    List<Tenant> filtered = _tenants!;

    // Apply status filter
    if (_statusFilter != 'all') {
      TenantStatus statusToFilter;
      switch (_statusFilter) {
        case 'active':
          statusToFilter = TenantStatus.active;
          break;
        case 'pending':
          statusToFilter = TenantStatus.pending;
          break;
        case 'moved_out':
          statusToFilter = TenantStatus.movedOut;
          break;
        default:
          statusToFilter = TenantStatus.active;
      }
      filtered = filtered.where((tenant) => tenant.status == statusToFilter).toList();
    }

    // Apply property filter
    if (_propertyFilter != null && _propertyFilter != 'all') {
      filtered = filtered.where((tenant) {
        final propertyName = _tenantPropertyNames[tenant.id];
        return propertyName != null && 
               _properties.any((p) => p.id == _propertyFilter && p.name == propertyName);
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((tenant) {
        final fullName = '${tenant.firstName} ${tenant.lastName}'.toLowerCase();
        final email = tenant.email.toLowerCase();
        final propertyName = _tenantPropertyNames[tenant.id]?.toLowerCase() ?? '';
        return fullName.contains(query) || 
               email.contains(query) || 
               propertyName.contains(query);
      }).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      int result;
      switch (_sortBy) {
        case 'name':
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
          break;
        case 'created':
          result = a.createdAt.compareTo(b.createdAt);
          break;
        case 'lease_start':
          final aDate = a.leaseStartDate ?? DateTime(1900);
          final bDate = b.leaseStartDate ?? DateTime(1900);
          result = aDate.compareTo(bDate);
          break;
        default:
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
      }
      return _sortAscending ? result : -result;
    });

    return filtered;
  }

  // Get paginated tenants
  List<Tenant> get _paginatedTenants {
    final filtered = _filteredTenants;
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;
    
    if (startIndex >= filtered.length) return [];
    
    return filtered.sublist(
      startIndex,
      endIndex > filtered.length ? filtered.length : endIndex,
    );
  }

  int get _totalPages {
    return (_filteredTenants.length / _itemsPerPage).ceil();
  }

  Future<void> _deleteTenant(Tenant tenant) async {
    // Capture scaffold messenger before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Delete'),
            content: Text(
              'Are you sure you want to delete ${tenant.firstName} ${tenant.lastName}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await serviceLocator.tenantService.deleteTenant(tenant.id);
        // Reload tenants after deletion
        await _loadTenants();

        if (!mounted) return;

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              '${tenant.firstName} ${tenant.lastName} deleted successfully',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to delete tenant: $e';
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error deleting tenant: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () => _deleteTenant(tenant),
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }

  Future<void> _navigateToAddTenant() async {
    // Capture scaffold messenger before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddTenantPage()),
    );

    if (result != null) {
      await _loadTenants();

      // Show success notification for new tenant
      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('New tenant added successfully'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _navigateToTenantDetails(Tenant tenant) async {
    // Capture scaffold messenger before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TenantDetailsPage(tenant: tenant),
      ),
    );

    if (result != null) {
      await _loadTenants();

      // Show success notification for tenant update
      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            '${tenant.firstName} ${tenant.lastName} updated successfully',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _navigateToRoomInfo(Tenant tenant) async {
    // Capture scaffold messenger before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TenantRoomInfoPage(tenant: tenant),
      ),
    );

    if (result != null) {
      await _loadTenants();

      // Show success notification for room changes
      if (!mounted) return;

      final message =
          result.roomId != null
              ? 'Room assignment updated successfully'
              : 'Tenant vacated successfully';

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tenants'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadTenants,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _currentPage = 1; // Reset to first page
                });
              },
              decoration: InputDecoration(
                hintText: 'Search tenants by name, email, or property...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                            _currentPage = 1;
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
              ),
            ),
          ),

          // Tenants count and filters
          if (_tenants != null && !_isLoading)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${_filteredTenants.length} tenant${_filteredTenants.length != 1 ? 's' : ''} found',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Row(
                        children: [
                          // Sort button
                          PopupMenuButton<String>(
                            icon: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                                  size: 16,
                                  color: Theme.of(context).primaryColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _getSortText(),
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            onSelected: (value) {
                              setState(() {
                                if (_sortBy == value) {
                                  _sortAscending = !_sortAscending;
                                } else {
                                  _sortBy = value;
                                  _sortAscending = true;
                                }
                                _currentPage = 1; // Reset to first page
                              });
                            },
                            itemBuilder: (context) => [
                              _buildSortMenuItem('name', 'Name', Icons.person),
                              _buildSortMenuItem('created', 'Created Date', Icons.date_range),
                              _buildSortMenuItem('lease_start', 'Lease Start', Icons.calendar_today),
                            ],
                          ),
                          const SizedBox(width: 8),
                          // Status filter
                          PopupMenuButton<String>(
                            icon: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.filter_list,
                                  color: Theme.of(context).primaryColor,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _getFilterText(),
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            onSelected: (value) {
                              setState(() {
                                _statusFilter = value;
                                _currentPage = 1; // Reset to first page
                              });
                            },
                            itemBuilder: (context) => [
                              _buildFilterMenuItem('all', 'All Tenants', Icons.people),
                              _buildFilterMenuItem('active', 'Active Tenants', Icons.check_circle),
                              _buildFilterMenuItem('pending', 'Pending Tenants', Icons.pending),
                              _buildFilterMenuItem('moved_out', 'Moved Out', Icons.logout),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Property filter dropdown
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            labelText: 'Filter by Property',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          value: _propertyFilter,
                          items: [
                            const DropdownMenuItem(
                              value: 'all',
                              child: Text('All Properties'),
                            ),
                            ..._properties.map((property) => DropdownMenuItem(
                              value: property.id,
                              child: Text(property.name),
                            )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _propertyFilter = value == 'all' ? null : value;
                              _currentPage = 1; // Reset to first page
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

          // Main content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                    ? _buildErrorState()
                    : _buildTenantsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _navigateToAddTenant,
        icon: const Icon(Icons.person_add),
        label: const Text('ADD TENANT'),
        tooltip: 'Add New Tenant',
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Tenants',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage ?? 'An unexpected error occurred',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadTenants,
            icon: const Icon(Icons.refresh),
            label: const Text('Try Again'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTenantsList() {
    final paginatedTenants = _paginatedTenants;
    
    if (_filteredTenants.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isNotEmpty ? Icons.search_off : Icons.people_outline,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'No tenants match your search'
                  : 'No tenants found',
              style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 24),
            if (_searchQuery.isNotEmpty)
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                    _searchQuery = '';
                    _currentPage = 1;
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('Clear Search'),
              )
            else
              ElevatedButton.icon(
                onPressed: _navigateToAddTenant,
                icon: const Icon(Icons.person_add),
                label: const Text('Add Tenant'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Tenants list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.only(bottom: 80), // Extra padding for FAB
            itemCount: paginatedTenants.length,
            itemBuilder: (context, index) {
              final tenant = paginatedTenants[index];
              return Card(
                margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () => _navigateToTenantDetails(tenant),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Avatar
                            CircleAvatar(
                              radius: 28,
                              backgroundColor: _getAvatarColor(tenant.firstName),
                              child: Text(
                                '${tenant.firstName[0]}${tenant.lastName[0]}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),

                            // Tenant info
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          '${tenant.firstName} ${tenant.lastName}',
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      _buildStatusChip(tenant.status),
                                    ],
                                  ),

                                  // Property information
                                  if (_tenantPropertyNames[tenant.id] != null) ...[
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(Icons.home, size: 16, color: Colors.blue),
                                        const SizedBox(width: 4),
                                        Text(
                                          _tenantPropertyNames[tenant.id]!,
                                          style: TextStyle(
                                            color: Colors.blue.shade700,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                  if (tenant.leaseStartDate != null && tenant.leaseEndDate != null) ...[
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        const Icon(Icons.date_range, size: 16, color: Colors.grey),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Lease: ${_formatDate(tenant.leaseStartDate!)} - ${_formatDate(tenant.leaseEndDate!)}',
                                          style: TextStyle(
                                            color: Colors.grey.shade700,
                                            fontSize: 13,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                  // Room information
                                  if (tenant.roomId != null && tenant.status != TenantStatus.movedOut) ...[
                                    const SizedBox(height: 8),
                                    FutureBuilder(
                                      future: serviceLocator.roomService.getRoomById(tenant.roomId!),
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState == ConnectionState.waiting) {
                                          return const SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(strokeWidth: 2),
                                          );
                                        } else if (snapshot.hasData && snapshot.data != null) {
                                          final room = snapshot.data!;
                                          return Row(
                                            children: [
                                              const Icon(Icons.meeting_room, size: 16, color: Colors.blue),
                                              const SizedBox(width: 4),
                                              Text(
                                                'Room: ${room.name}',
                                                style: TextStyle(
                                                  color: Colors.blue.shade700,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 13,
                                                ),
                                              ),
                                            ],
                                          );
                                        } else {
                                          return const SizedBox.shrink();
                                        }
                                      },
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Action buttons
                        Wrap(
                          alignment: WrapAlignment.end,
                          spacing: 4,
                          runSpacing: 4,
                          children: [
                            TextButton.icon(
                              onPressed: () => _navigateToTenantDetails(tenant),
                              icon: const Icon(Icons.visibility, size: 18),
                              label: const Text('View'),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                visualDensity: VisualDensity.compact,
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () => _navigateToTenantDetails(tenant),
                              icon: const Icon(Icons.edit, size: 18),
                              label: const Text('Edit'),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                visualDensity: VisualDensity.compact,
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () => _navigateToRoomInfo(tenant),
                              icon: const Icon(Icons.home, size: 18),
                              label: Text(
                                tenant.status == TenantStatus.movedOut
                                    ? 'Assign Room'
                                    : (tenant.roomId != null ? 'Room Info' : 'Assign Room'),
                              ),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                visualDensity: VisualDensity.compact,
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () => _deleteTenant(tenant),
                              icon: const Icon(Icons.delete, color: Colors.red, size: 18),
                              label: const Text('Delete', style: TextStyle(color: Colors.red)),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                visualDensity: VisualDensity.compact,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        
        // Pagination controls
        if (_totalPages > 1)
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Page $_currentPage of $_totalPages',
                  style: TextStyle(color: Colors.grey.shade700),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: _currentPage > 1 ? () {
                        setState(() {
                          _currentPage--;
                        });
                      } : null,
                      icon: const Icon(Icons.chevron_left),
                    ),
                    ...List.generate(
                      _totalPages > 5 ? 5 : _totalPages,
                      (index) {
                        int pageNumber;
                        if (_totalPages <= 5) {
                          pageNumber = index + 1;
                        } else {
                          if (_currentPage <= 3) {
                            pageNumber = index + 1;
                          } else if (_currentPage >= _totalPages - 2) {
                            pageNumber = _totalPages - 4 + index;
                          } else {
                            pageNumber = _currentPage - 2 + index;
                          }
                        }
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          child: TextButton(
                            onPressed: () {
                              setState(() {
                                _currentPage = pageNumber;
                              });
                            },
                            style: TextButton.styleFrom(
                              backgroundColor: _currentPage == pageNumber 
                                  ? Theme.of(context).primaryColor 
                                  : null,
                              foregroundColor: _currentPage == pageNumber 
                                  ? Colors.white 
                                  : null,
                              minimumSize: const Size(40, 40),
                            ),
                            child: Text('$pageNumber'),
                          ),
                        );
                      },
                    ),
                    IconButton(
                      onPressed: _currentPage < _totalPages ? () {
                        setState(() {
                          _currentPage++;
                        });
                      } : null,
                      icon: const Icon(Icons.chevron_right),
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildStatusChip(TenantStatus status) {
    MaterialColor color;
    String label;

    switch (status) {
      case TenantStatus.active:
        color = Colors.green;
        label = 'Active';
        break;
      case TenantStatus.pending:
        color = Colors.orange;
        label = 'Pending';
        break;
      case TenantStatus.movedOut:
        color = Colors.grey;
        label = 'Moved Out';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color[700],
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Helper function to format dates
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Helper function to get the filter button text
  String _getFilterText() {
    switch (_statusFilter) {
      case 'active':
        return 'Active';
      case 'pending':
        return 'Pending';
      case 'moved_out':
        return 'Moved Out';
      default:
        return 'All';
    }
  }

  // Helper function to build filter menu items
  PopupMenuItem<String> _buildFilterMenuItem(
    String value,
    String label,
    IconData icon,
  ) {
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: _statusFilter == value
                ? Theme.of(context).primaryColor
                : Colors.grey.shade700,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontWeight: _statusFilter == value ? FontWeight.bold : FontWeight.normal,
              color: _statusFilter == value ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  // Helper function to generate avatar colors based on name
  Color _getAvatarColor(String name) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    
    final index = name.hashCode % colors.length;
    return colors[index.abs()];
  }

  // Helper function to get the sort button text
  String _getSortText() {
    switch (_sortBy) {
      case 'name':
        return 'Name';
      case 'created':
        return 'Created';
      case 'lease_start':
        return 'Lease';
      default:
        return 'Name';
    }
  }

  // Helper function to build sort menu items
  PopupMenuItem<String> _buildSortMenuItem(
    String value,
    String label,
    IconData icon,
  ) {
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: _sortBy == value
                ? Theme.of(context).primaryColor
                : Colors.grey.shade700,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontWeight: _sortBy == value ? FontWeight.bold : FontWeight.normal,
              color: _sortBy == value ? Theme.of(context).primaryColor : null,
            ),
          ),
          if (_sortBy == value)
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: Icon(
                _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 16,
                color: Theme.of(context).primaryColor,
              ),
            ),
        ],
      ),
    );
  }
}
