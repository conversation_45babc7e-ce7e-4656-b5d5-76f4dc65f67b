-- Create tenants table to store tenant information
CREATE TABLE IF NOT EXISTS public.tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    phone_number TEXT,
    lease_start_date DATE,
    lease_end_date DATE,
    status TEXT NOT NULL DEFAULT 'pending',
    room_id UUID REFERENCES public.rooms(id) ON DELETE SET NULL,
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ
);

-- Create index for better performance when searching by email
CREATE INDEX IF NOT EXISTS idx_tenants_email ON public.tenants(email);

-- Create index for better performance when searching by room_id
CREATE INDEX IF NOT EXISTS idx_tenants_room_id ON public.tenants(room_id);

-- Create index for searching by status
CREATE INDEX IF NOT EXISTS idx_tenants_status ON public.tenants(status);

-- Add RLS policies for tenant table
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;

-- Create a tenant role type in the system
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE public.user_role AS ENUM ('admin', 'manager', 'tenant');
    END IF;
END
$$;

-- Create user_profiles table to store role information
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    role public.user_role NOT NULL DEFAULT 'tenant',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ
);

-- Enable RLS on user_profiles
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_profiles - consolidated for optimal performance
-- First, drop all existing policies to avoid conflicts or multiple permissive policies
DROP POLICY IF EXISTS "Authenticated users can access all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;

-- Create a single consolidated policy that uses a subselect for auth.uid() to optimize performance
CREATE POLICY "Authenticated access policy"
    ON public.user_profiles
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create a function to create a profile entry when a new user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the new user has the tenant metadata
    IF NEW.raw_user_meta_data->>'role' = 'tenant' THEN
        INSERT INTO public.user_profiles (id, role)
        VALUES (NEW.id, 'tenant'::public.user_role);
    ELSE
        INSERT INTO public.user_profiles (id, role)
        VALUES (NEW.id, 'admin'::public.user_role);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Create a trigger to call the function when a new user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Create a function to check if a user has a specific role
CREATE OR REPLACE FUNCTION public.user_has_role(required_role public.user_role)
RETURNS BOOLEAN AS $$
DECLARE
    current_user_id UUID := (SELECT auth.uid());
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = current_user_id AND role = required_role
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public, auth;

-- Policy for full tenant access
DROP POLICY IF EXISTS "Tenants can view their own tenant record" ON public.tenants;
DROP POLICY IF EXISTS "Authenticated users can access all tenant records" ON public.tenants;
CREATE POLICY "Authenticated users can access all tenant records"
    ON public.tenants
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Drop any existing limited policies
DROP POLICY IF EXISTS tenant_insert_policy ON public.tenants;
DROP POLICY IF EXISTS tenant_update_policy ON public.tenants;
DROP POLICY IF EXISTS tenant_delete_policy ON public.tenants;

-- Create a function to automatically link tenant auth user with tenant record
CREATE OR REPLACE FUNCTION public.link_tenant_to_auth_user()
RETURNS TRIGGER AS $$
DECLARE
    matching_user_id UUID;
BEGIN
    -- Get matching user ID once to avoid multiple evaluations
    SELECT id INTO matching_user_id FROM auth.users WHERE email = NEW.email LIMIT 1;
    
    -- If the new tenant has a Supabase auth user with matching email
    -- Update the tenant record with the auth user's ID in metadata
    IF matching_user_id IS NOT NULL THEN
        UPDATE public.tenants
        SET notes = COALESCE(notes, '') || ' Linked to auth user: ' || matching_user_id
        WHERE id = NEW.id;
    END IF;
      
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public, auth;

-- Create a trigger to call the function when a new tenant is created
DROP TRIGGER IF EXISTS on_tenant_created ON public.tenants;
CREATE TRIGGER on_tenant_created
    AFTER INSERT ON public.tenants
    FOR EACH ROW
    EXECUTE FUNCTION public.link_tenant_to_auth_user();

-- Add comments to the table and columns for documentation
COMMENT ON TABLE public.tenants IS 'Stores information about tenants';
COMMENT ON COLUMN public.tenants.id IS 'Unique identifier for each tenant';
COMMENT ON COLUMN public.tenants.email IS 'Email address of the tenant, used for login';
COMMENT ON COLUMN public.tenants.first_name IS 'First name of the tenant';
COMMENT ON COLUMN public.tenants.last_name IS 'Last name of the tenant';
COMMENT ON COLUMN public.tenants.phone_number IS 'Contact phone number of the tenant';
COMMENT ON COLUMN public.tenants.lease_start_date IS 'Date when tenant lease begins';
COMMENT ON COLUMN public.tenants.lease_end_date IS 'Date when tenant lease ends';
COMMENT ON COLUMN public.tenants.status IS 'Current status of the tenant (active, pending, movedOut)';
COMMENT ON COLUMN public.tenants.room_id IS 'Reference to the room occupied by the tenant';
COMMENT ON COLUMN public.tenants.emergency_contact_name IS 'Name of emergency contact person';
COMMENT ON COLUMN public.tenants.emergency_contact_phone IS 'Phone number of emergency contact person';
COMMENT ON COLUMN public.tenants.notes IS 'Additional notes about the tenant';
COMMENT ON COLUMN public.tenants.created_at IS 'Timestamp when the tenant record was created';
COMMENT ON COLUMN public.tenants.updated_at IS 'Timestamp when the tenant record was last updated'; 
 