import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/tenant/tenant.dart';
import 'package:logging/logging.dart';

class TenantService {
  final SupabaseClient _client;
  final Logger _logger = Logger('TenantService');

  TenantService(this._client);

  // Get all tenants
  Future<List<Tenant>> getAllTenants() async {
    try {
      final response = await _client
          .from('tenants')
          .select()
          .order('created_at', ascending: false);

      return response.map<Tenant>((json) => Tenant.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting tenants: $e');
      rethrow;
    }
  }

  // Get tenant by ID
  Future<Tenant?> getTenantById(String id) async {
    try {
      final response =
          await _client.from('tenants').select().eq('id', id).single();

      return Tenant.fromJson(response);
    } catch (e) {
      _logger.severe('Error getting tenant by ID: $e');
      return null;
    }
  }

  // Get tenant by user ID
  Future<Tenant?> getTenantByUserId(String userId) async {
    try {
      // First try using the SQL function to get tenant by user ID
      try {
        final response = await _client
            .rpc('get_tenant_by_user_id', params: {'user_id_param': userId})
            .maybeSingle();
        
        if (response == null) {
          _logger.info('No tenant found for user ID: $userId via RPC');
          // Continue to direct query
        } else {
          return Tenant.fromJson(response);
        }
      } catch (e) {
        _logger.warning('RPC method failed, trying direct query: $e');
        // Continue to direct query
      }
      
      // If the RPC method fails or returns null, try a direct query as fallback
      final response = await _client
          .from('tenants')
          .select()
          .eq('user_id', userId)
          .maybeSingle();
      
      if (response != null) {
        return Tenant.fromJson(response);
      }
      
      // If we still don't have a tenant, log it and return null
      _logger.info('No tenant found for user ID: $userId via direct query');
      return null;
    } catch (e) {
      _logger.severe('Error getting tenant by user ID: $e');
      return null;
    }
  }

  // Get tenants by room ID
  Future<List<Tenant>> getTenantsByRoomId(String roomId) async {
    try {
      final response = await _client
          .from('tenants')
          .select()
          .eq('room_id', roomId);

      return response.map<Tenant>((json) => Tenant.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting tenants by room ID: $e');
      rethrow;
    }
  }

  // Save tenant data to Supabase
  Future<Tenant> saveTenantData(Tenant tenant) async {
    try {
      final response =
          await _client
              .from('tenants')
              .insert(tenant.toJson())
              .select()
              .single();

      return Tenant.fromJson(response);
    } catch (e) {
      _logger.severe('Error saving tenant data: $e');
      rethrow;
    }
  }

  // Create Supabase Auth user for tenant via Edge Function
  Future<Map<String, dynamic>> createSupabaseAuthUser(
    String email,
    String firstName,
    String lastName,
  ) async {
    try {
      // Use Edge Function to create user (securely uses service role)
      final response = await _client.functions.invoke(
        'create-tenant-user',
        body: {
          'email': email,
          'firstName': firstName,
          'lastName': lastName,
          'role': 'tenant',
          'loginUrl':
              'https://tenanta-app.com/login', // Update with your actual app URL
        },
      );

      if (response.status != 200) {
        throw Exception('Failed to create tenant user: ${response.data}');
      }

      return response.data;
    } catch (e) {
      _logger.severe('Error creating Supabase Auth user: $e');
      rethrow;
    }
  }

  // Send welcome email to tenant
  Future<void> sendWelcomeEmail(String email, String firstName) async {
    try {
      // Use Supabase Edge Functions for sending emails
      await _client.functions.invoke(
        'send-welcome-email',
        body: {
          'email': email,
          'firstName': firstName,
          'loginUrl':
              'https://tenant-app-url.com/login', // Replace with your tenant app URL
        },
      );

      // Log the notification
      _logger.info('Welcome email sent to tenant: $email');
    } catch (e) {
      _logger.severe('Error sending welcome email: $e');
      rethrow;
    }
  }

  // Create tenant account with email notification
  Future<Tenant> createTenantWithNotification(Tenant tenant) async {
    try {
      // 1. Save tenant data
      final savedTenant = await saveTenantData(tenant);
      _logger.info('Tenant data saved successfully: ${savedTenant.id}');

      // 2. Create Supabase Auth user and send welcome email (handled by the Edge Function)
      _logger.info('Creating auth user for tenant: ${tenant.email}');

      // Add retry logic for the edge function
      Map<String, dynamic> userResponse = {};
      int retries = 0;
      bool success = false;

      while (retries < 2 && !success) {
        try {
          userResponse = await createSupabaseAuthUser(
            tenant.email,
            tenant.firstName,
            tenant.lastName,
          );
          success = true;
        } catch (e) {
          retries++;
          _logger.warning('Attempt $retries failed: ${e.toString()}');
          if (retries < 2) {
            await Future.delayed(
              const Duration(seconds: 2),
            ); // Wait before retry
          } else {
            rethrow; // Rethrow if all retries fail
          }
        }
      }

      // Log email status
      final bool passwordResetSent = userResponse['passwordResetSent'] ?? false;
      // Change to non-final to allow reassignment
      bool welcomeEmailStatus = userResponse['welcomeEmailSent'] ?? false;
      _logger.info(
        'Email status - Password reset: $passwordResetSent, Welcome email: $welcomeEmailStatus',
      );

      // Update tenant notes to include email status
      String emailStatus =
          'email status: reset: $passwordResetSent, welcome: $welcomeEmailStatus';
      String updatedNotes =
          savedTenant.notes != null
              ? '${savedTenant.notes}\n$emailStatus'
              : emailStatus;

      // Try to send welcome email if it wasn't sent by the Edge Function
      if (!welcomeEmailStatus) {
        _logger.info(
          'Welcome email not sent by Edge Function, trying direct send...',
        );
        try {
          await sendWelcomeEmail(tenant.email, tenant.firstName);
          _logger.info('Direct welcome email sent successfully');

          // Update notes with success
          updatedNotes = '$updatedNotes\nDirect welcome email: success';
          welcomeEmailStatus = true;
        } catch (emailError) {
          _logger.warning('Failed to send direct welcome email: $emailError');
          // Continue with the process even if welcome email fails

          // Update notes with failure
          updatedNotes = '$updatedNotes\nDirect welcome email: failed';
        }
      }

      // 3. Update tenant with user_id if available and final notes
      if (userResponse['user'] != null && userResponse['user']['id'] != null) {
        final userId = userResponse['user']['id'] as String;
        _logger.info('Linking tenant to auth user: $userId');

        // Update the tenant with the user ID and notes
        await _client
            .from('tenants')
            .update({
              'user_id': userId,
              'updated_at': DateTime.now().toIso8601String(),
              'notes': updatedNotes,
              'welcome_email_sent': welcomeEmailStatus,
              'password_reset_sent': passwordResetSent,
            })
            .eq('id', savedTenant.id);
      } else {
        _logger.warning(
          'User ID not found in response: ${userResponse.toString()}',
        );

        // Update tenant notes with email status even if no user ID
        await _client
            .from('tenants')
            .update({
              'updated_at': DateTime.now().toIso8601String(),
              'notes': updatedNotes,
              'welcome_email_sent': welcomeEmailStatus,
              'password_reset_sent': passwordResetSent,
            })
            .eq('id', savedTenant.id);
      }

      // Return updated tenant with notes and email status
      return Tenant(
        id: savedTenant.id,
        email: savedTenant.email,
        firstName: savedTenant.firstName,
        lastName: savedTenant.lastName,
        phoneNumber: savedTenant.phoneNumber,
        leaseStartDate: savedTenant.leaseStartDate,
        leaseEndDate: savedTenant.leaseEndDate,
        status: savedTenant.status,
        roomId: savedTenant.roomId,
        emergencyContactName: savedTenant.emergencyContactName,
        emergencyContactPhone: savedTenant.emergencyContactPhone,
        notes: updatedNotes,
        userId: userResponse['user']?['id'] as String? ?? savedTenant.userId,
        createdAt: savedTenant.createdAt,
        updatedAt: DateTime.now(),
        welcomeEmailSent: welcomeEmailStatus,
        passwordResetSent: passwordResetSent,
      );
    } catch (e) {
      _logger.severe('Error creating tenant with notification: $e');
      rethrow;
    }
  }

  // Update tenant data
  Future<Tenant> updateTenant(Tenant tenant) async {
    try {
      // First check if the tenant exists
      final existingTenant = await getTenantById(tenant.id);
      if (existingTenant == null) {
        _logger.severe('Tenant not found with ID: ${tenant.id}');
        throw Exception('Tenant not found with ID: ${tenant.id}');
      }

      final response =
          await _client
              .from('tenants')
              .update(tenant.copyWith(updatedAt: DateTime.now()).toJson())
              .eq('id', tenant.id)
              .select()
              .single();

      return Tenant.fromJson(response);
    } catch (e) {
      _logger.severe('Error updating tenant: $e');
      rethrow;
    }
  }

  // Delete tenant
  Future<void> deleteTenant(String id) async {
    try {
      await _client.from('tenants').delete().eq('id', id);
    } catch (e) {
      _logger.severe('Error deleting tenant: $e');
      rethrow;
    }
  }

  // Assign tenant to room
  Future<Tenant> assignTenantToRoom(String tenantId, String roomId) async {
    try {
      final tenant = await getTenantById(tenantId);
      if (tenant == null) {
        throw Exception('Tenant not found');
      }

      final updatedTenant = tenant.copyWith(
        roomId: roomId,
        updatedAt: DateTime.now(),
      );

      return await updateTenant(updatedTenant);
    } catch (e) {
      _logger.severe('Error assigning tenant to room: $e');
      rethrow;
    }
  }

  // Update tenant status
  Future<Tenant> updateTenantStatus(
    String tenantId,
    TenantStatus status,
  ) async {
    try {
      final tenant = await getTenantById(tenantId);
      if (tenant == null) {
        throw Exception('Tenant not found');
      }

      // If status is being set to movedOut, also clear the roomId
      final updatedTenant =
          status == TenantStatus.movedOut
              ? tenant.copyWith(
                status: status,
                roomId: null, // Clear room assignment when tenant moves out
                updatedAt: DateTime.now(),
              )
              : tenant.copyWith(status: status, updatedAt: DateTime.now());

      return await updateTenant(updatedTenant);
    } catch (e) {
      _logger.severe('Error updating tenant status: $e');
      rethrow;
    }
  }

  // Check if email already exists
  Future<bool> checkEmailExists(String email) async {
    try {
      final response = await _client
          .from('tenants')
          .select('email')
          .eq('email', email)
          .limit(1);

      return response.isNotEmpty;
    } catch (e) {
      _logger.severe('Error checking if email exists: $e');
      rethrow;
    }
  }
}
