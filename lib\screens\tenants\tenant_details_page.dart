import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../models/tenant/tenant.dart';
import '../../models/room/room_model.dart';
import '../../models/activity_log.dart';
import '../../models/bill/bill.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../widgets/app_loading_indicator.dart';
import 'assign_room_page.dart';
import '../bills/bill_detail_page.dart';
import '../bills/add_bill_page.dart';

class TenantDetailsPage extends StatefulWidget {
  final Tenant tenant;

  const TenantDetailsPage({super.key, required this.tenant});

  @override
  State<TenantDetailsPage> createState() => _TenantDetailsPageState();
}

class _TenantDetailsPageState extends State<TenantDetailsPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _isEditing = false;
  late final TabController _tabController;
  Room? _currentRoom;

  // Form fields
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emergencyContactNameController;
  late final TextEditingController _emergencyContactPhoneController;
  late final TextEditingController _notesController;
  late DateTime? _leaseStartDate;
  late DateTime? _leaseEndDate;
  late TenantStatus _status;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeControllers();
    _status = widget.tenant.status;
    _fetchCurrentRoom();
  }

  void _initializeControllers() {
    _firstNameController = TextEditingController(text: widget.tenant.firstName);
    _lastNameController = TextEditingController(text: widget.tenant.lastName);
    _emailController = TextEditingController(text: widget.tenant.email);
    _phoneController = TextEditingController(
      text: widget.tenant.phoneNumber ?? '',
    );
    _emergencyContactNameController = TextEditingController(
      text: widget.tenant.emergencyContactName ?? '',
    );
    _emergencyContactPhoneController = TextEditingController(
      text: widget.tenant.emergencyContactPhone ?? '',
    );
    _notesController = TextEditingController(text: widget.tenant.notes ?? '');
    _leaseStartDate = widget.tenant.leaseStartDate;
    _leaseEndDate = widget.tenant.leaseEndDate;
  }

  Future<void> _fetchCurrentRoom() async {
    // Don't fetch room details if tenant has moved out
    if (widget.tenant.status == TenantStatus.movedOut) {
      setState(() {
        _currentRoom = null;
      });
      return;
    }

    if (widget.tenant.roomId != null) {
      final room = await serviceLocator.roomService.getRoomById(
        widget.tenant.roomId!,
      );
      setState(() {
        _currentRoom = room;
      });
    } else {
      setState(() {
        _currentRoom = null;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _emergencyContactNameController.dispose();
    _emergencyContactPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Date picker for lease start date
  Future<void> _selectLeaseStartDate(BuildContext context) async {
    if (!_isEditing) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _leaseStartDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _leaseStartDate) {
      setState(() {
        _leaseStartDate = picked;
        // If end date is before start date, update end date
        if (_leaseEndDate != null &&
            _leaseEndDate!.isBefore(_leaseStartDate!)) {
          _leaseEndDate = _leaseStartDate!.add(const Duration(days: 365));
        }
      });
    }
  }

  // Date picker for lease end date
  Future<void> _selectLeaseEndDate(BuildContext context) async {
    if (!_isEditing) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _leaseEndDate ??
          (_leaseStartDate?.add(const Duration(days: 365)) ??
              DateTime.now().add(const Duration(days: 365))),
      firstDate: _leaseStartDate ?? DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _leaseEndDate) {
      setState(() {
        _leaseEndDate = picked;
      });
    }
  }

  // Save tenant changes
  Future<void> _saveTenant() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        // Create updated tenant object
        final updatedTenant = widget.tenant.copyWith(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          email: _emailController.text.trim(),
          phoneNumber:
              _phoneController.text.trim().isNotEmpty
                  ? _phoneController.text.trim()
                  : null,
          leaseStartDate: _leaseStartDate,
          leaseEndDate: _leaseEndDate,
          status: _status,
          emergencyContactName:
              _emergencyContactNameController.text.trim().isNotEmpty
                  ? _emergencyContactNameController.text.trim()
                  : null,
          emergencyContactPhone:
              _emergencyContactPhoneController.text.trim().isNotEmpty
                  ? _emergencyContactPhoneController.text.trim()
                  : null,
          notes:
              _notesController.text.trim().isNotEmpty
                  ? _notesController.text.trim()
                  : null,
        );

        // Update tenant
        final savedTenant = await serviceLocator.tenantService.updateTenant(
          updatedTenant,
        );

        // Exit editing mode and show success message
        setState(() {
          _isLoading = false;
          _isEditing = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tenant updated successfully'),
              behavior: SnackBarBehavior.floating,
            ),
          );
          Navigator.pop(context, savedTenant);
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  // Toggle editing mode
  void _toggleEditing() {
    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        // Reset form if cancelling edit
        _initializeControllers();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          '${widget.tenant.firstName} ${widget.tenant.lastName}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade600, Colors.blue.shade700],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 4,
        shadowColor: Colors.blue.withValues(alpha: 0.3),
        centerTitle: false,
        foregroundColor: Colors.white,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit_outlined),
              tooltip: 'Edit Tenant',
              onPressed: _toggleEditing,
            )
          else
            IconButton(
              icon: const Icon(Icons.close),
              tooltip: 'Cancel Editing',
              onPressed: _toggleEditing,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 14,
          ),
          tabs: const [
            Tab(text: 'Details', icon: Icon(Icons.info_outline)),
            Tab(text: 'Bills', icon: Icon(Icons.receipt_long)),
            Tab(text: 'History', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'Saving changes...')
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildForm(),
                  _TenantBillsTab(tenantId: widget.tenant.id),
                  _TenantHistoryTab(tenantId: widget.tenant.id),
                ],
              ),
      floatingActionButton:
          _isEditing
              ? FloatingActionButton.extended(
                onPressed: _saveTenant,
                icon: const Icon(Icons.save_outlined),
                label: const Text('Save Changes'),
                elevation: 2,
                backgroundColor: Colors.blue,
              )
              : null,
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status selector
            _buildSectionHeader('Status'),

            if (_isEditing)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: DropdownButtonFormField<TenantStatus>(
                  value: _status,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Colors.blue,
                        width: 2,
                      ),
                    ),
                    prefixIcon: const Icon(Icons.person_pin_circle),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items:
                      TenantStatus.values.map((status) {
                        String label;
                        Color textColor;

                        switch (status) {
                          case TenantStatus.active:
                            label = 'Active';
                            textColor = Colors.green;
                            break;
                          case TenantStatus.pending:
                            label = 'Pending';
                            textColor = Colors.orange;
                            break;
                          case TenantStatus.movedOut:
                            label = 'Moved Out';
                            textColor = Colors.grey;
                            break;
                        }

                        return DropdownMenuItem(
                          value: status,
                          child: Text(
                            label,
                            style: TextStyle(
                              color: textColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _status = value;
                      });
                    }
                  },
                  dropdownColor: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  icon: const Icon(
                    Icons.arrow_drop_down_circle_outlined,
                    color: Colors.blue,
                  ),
                ),
              )
            else
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _buildStatusChip(_status),
              ),

            // Tenant information section
            _buildSectionHeader('Tenant Information'),
            _buildInfoCard(
              child: Column(
                children: [
                  // First Name and Last Name in a row
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // First Name - 50% width
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8, bottom: 16),
                          child:
                              _isEditing
                                  ? TextFormField(
                                    controller: _firstNameController,
                                    decoration: InputDecoration(
                                      labelText: 'First Name *',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: const Icon(Icons.person),
                                      filled: true,
                                      fillColor: Colors.white,
                                      labelStyle: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                    ),
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return 'Please enter first name';
                                      }
                                      return null;
                                    },
                                  )
                                  : _buildReadOnlyField(
                                    label: 'First Name',
                                    value: _firstNameController.text,
                                    icon: Icons.person,
                                  ),
                        ),
                      ),
                      // Last Name - 50% width
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 8, bottom: 16),
                          child:
                              _isEditing
                                  ? TextFormField(
                                    controller: _lastNameController,
                                    decoration: InputDecoration(
                                      labelText: 'Last Name *',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: const Icon(
                                        Icons.person_outline,
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      labelStyle: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                    ),
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        return 'Please enter last name';
                                      }
                                      return null;
                                    },
                                  )
                                  : _buildReadOnlyField(
                                    label: 'Last Name',
                                    value: _lastNameController.text,
                                    icon: Icons.person_outline,
                                  ),
                        ),
                      ),
                    ],
                  ),

                  // Email - Full width
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child:
                        _isEditing
                            ? TextFormField(
                              controller: _emailController,
                              decoration: InputDecoration(
                                labelText: 'Email *',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(Icons.email),
                                filled: true,
                                fillColor: Colors.white,
                                labelStyle: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Please enter email';
                                }
                                if (!RegExp(
                                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                ).hasMatch(value)) {
                                  return 'Please enter a valid email';
                                }
                                return null;
                              },
                            )
                            : _buildReadOnlyField(
                              label: 'Email',
                              value: _emailController.text,
                              icon: Icons.email,
                              enableCopy: true,
                            ),
                  ),

                  // Phone - Full width
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child:
                        _isEditing
                            ? TextFormField(
                              controller: _phoneController,
                              decoration: InputDecoration(
                                labelText: 'Phone Number',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(Icons.phone),
                                filled: true,
                                fillColor: Colors.white,
                                hintText: 'Optional',
                                labelStyle: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              keyboardType: TextInputType.phone,
                            )
                            : _buildReadOnlyField(
                              label: 'Phone Number',
                              value: _phoneController.text,
                              icon: Icons.phone,
                              placeholder: 'Not provided',
                              enableCopy: true,
                            ),
                  ),
                ],
              ),
            ),

            // Room information section
            _buildSectionHeader('Room Information'),

            // Room information card - only show if tenant has a room assigned
            FutureBuilder<Widget>(
              future: _buildRoomInfoCard(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                return snapshot.data ?? Container();
              },
            ),

            // Lease information section
            _buildSectionHeader('Lease Information'),
            _buildInfoCard(
              child: Column(
                children: [
                  // Lease Start Date and End Date in a row
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Lease Start Date - 50% width
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8, bottom: 4),
                          child:
                              _isEditing
                                  ? InkWell(
                                    onTap: () => _selectLeaseStartDate(context),
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        labelText: 'Lease Start Date',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        prefixIcon: const Icon(
                                          Icons.calendar_today,
                                        ),
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintText: 'Optional',
                                        labelStyle: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              _leaseStartDate != null
                                                  ? DateFormat(
                                                    'MMM dd, yyyy',
                                                  ).format(_leaseStartDate!)
                                                  : 'Not set',
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                color:
                                                    _leaseStartDate == null
                                                        ? Colors.grey
                                                        : Colors.black87,
                                                fontWeight:
                                                    _leaseStartDate != null
                                                        ? FontWeight.w500
                                                        : FontWeight.normal,
                                              ),
                                            ),
                                          ),
                                          const Icon(
                                            Icons.arrow_drop_down,
                                            color: Colors.grey,
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                  : _buildReadOnlyField(
                                    label: 'Lease Start Date',
                                    value:
                                        _leaseStartDate != null
                                            ? DateFormat(
                                              'MMM dd, yyyy',
                                            ).format(_leaseStartDate!)
                                            : '',
                                    icon: Icons.calendar_today,
                                    placeholder: 'Not set',
                                  ),
                        ),
                      ),
                      // Lease End Date - 50% width
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 8, bottom: 4),
                          child:
                              _isEditing
                                  ? InkWell(
                                    onTap: () => _selectLeaseEndDate(context),
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        labelText: 'Lease End Date',
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        prefixIcon: const Icon(Icons.event),
                                        filled: true,
                                        fillColor: Colors.white,
                                        hintText: 'Optional',
                                        labelStyle: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 12,
                                            ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              _leaseEndDate != null
                                                  ? DateFormat(
                                                    'MMM dd, yyyy',
                                                  ).format(_leaseEndDate!)
                                                  : 'Not set',
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                color:
                                                    _leaseEndDate == null
                                                        ? Colors.grey
                                                        : Colors.black87,
                                                fontWeight:
                                                    _leaseEndDate != null
                                                        ? FontWeight.w500
                                                        : FontWeight.normal,
                                              ),
                                            ),
                                          ),
                                          const Icon(
                                            Icons.arrow_drop_down,
                                            color: Colors.grey,
                                          ),
                                        ],
                                      ),
                                    ),
                                  )
                                  : _buildReadOnlyField(
                                    label: 'Lease End Date',
                                    value:
                                        _leaseEndDate != null
                                            ? DateFormat(
                                              'MMM dd, yyyy',
                                            ).format(_leaseEndDate!)
                                            : '',
                                    icon: Icons.event,
                                    placeholder: 'Not set',
                                  ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Emergency Contact section
            _buildSectionHeader('Emergency Contact'),
            _buildInfoCard(
              child: Column(
                children: [
                  // Emergency Contact Name and Phone in a row
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Emergency Contact Name - 50% width
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8, bottom: 4),
                          child:
                              _isEditing
                                  ? TextFormField(
                                    controller: _emergencyContactNameController,
                                    decoration: InputDecoration(
                                      labelText: 'Emergency Contact Name',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: const Icon(Icons.contacts),
                                      filled: true,
                                      fillColor: Colors.white,
                                      hintText: 'Optional',
                                      labelStyle: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                    ),
                                  )
                                  : _buildReadOnlyField(
                                    label: 'Emergency Contact Name',
                                    value: _emergencyContactNameController.text,
                                    icon: Icons.contacts,
                                    placeholder: 'Not provided',
                                  ),
                        ),
                      ),
                      // Emergency Contact Phone - 50% width
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 8, bottom: 4),
                          child:
                              _isEditing
                                  ? TextFormField(
                                    controller:
                                        _emergencyContactPhoneController,
                                    decoration: InputDecoration(
                                      labelText: 'Emergency Contact Phone',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: const Icon(
                                        Icons.phone_forwarded,
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                      hintText: 'Optional',
                                      labelStyle: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                    ),
                                    keyboardType: TextInputType.phone,
                                  )
                                  : _buildReadOnlyField(
                                    label: 'Emergency Contact Phone',
                                    value:
                                        _emergencyContactPhoneController.text,
                                    icon: Icons.phone_forwarded,
                                    placeholder: 'Not provided',
                                    enableCopy: true,
                                  ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Additional Information section
            _buildSectionHeader('Additional Information'),
            _buildInfoCard(
              child: Column(
                children: [
                  // Notes - Full width
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child:
                        _isEditing
                            ? TextFormField(
                              controller: _notesController,
                              decoration: InputDecoration(
                                labelText: 'Notes',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(Icons.note),
                                filled: true,
                                fillColor: Colors.white,
                                hintText: 'Optional',
                                labelStyle: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              maxLines: 3,
                            )
                            : _buildReadOnlyField(
                              label: 'Notes',
                              value: _notesController.text,
                              icon: Icons.note,
                              placeholder: 'No notes added',
                              maxLines: 3,
                            ),
                  ),
                ],
              ),
            ),

            // Error message
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16, top: 12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.shade200),
                ),
                width: double.infinity,
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 60), // Space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildReadOnlyField({
    required String label,
    required String value,
    required IconData icon,
    String placeholder = 'Not set',
    int maxLines = 1,
    bool enableCopy = false,
  }) {
    final hasValue = value.isNotEmpty;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: Tooltip(
          message: enableCopy && hasValue ? 'Tap to copy $label' : '',
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: enableCopy && hasValue ? () => _copyToClipboard(value, label) : null,
            hoverColor: enableCopy && hasValue ? Colors.blue.withValues(alpha: 0.05) : null,
            splashColor: enableCopy && hasValue ? Colors.blue.withValues(alpha: 0.1) : null,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Label with copy icon
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          icon,
                          size: 18,
                          color: Colors.blue.shade600,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          label,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                            letterSpacing: 0.2,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (enableCopy && hasValue) ...[
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            Icons.copy,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Value
                  SelectableText(
                    hasValue ? value : placeholder,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: hasValue ? FontWeight.w600 : FontWeight.normal,
                      color: hasValue ? Colors.black87 : Colors.grey.shade500,
                      height: 1.4,
                    ),
                    maxLines: maxLines,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Copy to clipboard functionality
  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('$label copied to clipboard'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildInfoCard({required Widget child}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: child,
    );
  }

  Widget _buildStatusChip(TenantStatus status) {
    Color backgroundColor;
    Color textColor;
    String label;
    IconData icon;

    switch (status) {
      case TenantStatus.active:
        backgroundColor = Colors.green;
        textColor = Colors.green.shade700;
        label = 'Active';
        icon = Icons.check_circle;
        break;
      case TenantStatus.pending:
        backgroundColor = Colors.orange;
        textColor = Colors.orange.shade700;
        label = 'Pending';
        icon = Icons.schedule;
        break;
      case TenantStatus.movedOut:
        backgroundColor = Colors.grey;
        textColor = Colors.grey.shade700;
        label = 'Moved Out';
        icon = Icons.logout;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            backgroundColor.withValues(alpha: 0.1),
            backgroundColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: backgroundColor.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.15),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: backgroundColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: textColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 16,
                letterSpacing: 0.5,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16, top: 16),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 24,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.3,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build room information card
  Future<Widget> _buildRoomInfoCard() async {
    // If tenant has moved out or doesn't have a room assigned
    if (widget.tenant.status == TenantStatus.movedOut ||
        widget.tenant.roomId == null) {
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.meeting_room_outlined,
                  color: Colors.grey.shade600,
                  size: 22,
                ),
                const SizedBox(width: 12),
                const Text(
                  'No Room Assigned',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),

            // Show assign room button if not in editing mode
            // Allow assigning room even for moved out tenants
            if (!_isEditing) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _assignRoom,
                  icon: const Icon(Icons.add_home_outlined, size: 18),
                  label: const Text('Assign Room'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    }

    try {
      // Get room details
      final room = await serviceLocator.roomService.getRoomById(
        widget.tenant.roomId!,
      );
      if (room == null) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.red.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade600,
                    size: 22,
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      'Room not found (ID: ${widget.tenant.roomId})',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }

      // Get property details
      final property = await serviceLocator.propertyService.getPropertyById(
        room.propertyId,
      );
      final propertyName = property?.name ?? 'Unknown Property';

      // Build room info card
      return _buildInfoCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Room name and property
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        room.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.home,
                            size: 14,
                            color: Colors.blue.shade400,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              propertyName,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade100),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withAlpha(26),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '${CurrencyFormatter.formatAmount(room.rentalPrice)}/month',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Room details
            Row(
              children: [
                _buildRoomDetailItem(
                  icon: Icons.category,
                  label: 'Type',
                  value: room.roomTypeName,
                ),
                const SizedBox(width: 16),
                _buildRoomDetailItem(
                  icon: Icons.chair,
                  label: 'Furnished',
                  value: room.isFurnished ? 'Yes' : 'No',
                ),
              ],
            ),

            // Amenities
            if (room.amenities.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Amenities',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    room.amenities.map((amenity) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Text(
                          amenity.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade800,
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ],

            const SizedBox(height: 16),

            // Action button - not shown in editing mode
            if (!_isEditing)
              SizedBox(
                width: double.infinity,
                child:
                    _status == TenantStatus.movedOut
                        ? ElevatedButton.icon(
                          onPressed: _assignRoom,
                          icon: const Icon(Icons.add_home_outlined, size: 18),
                          label: const Text('Assign Room'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                        )
                        : ElevatedButton.icon(
                          onPressed: () => _vacateRoom(room.id),
                          icon: const Icon(Icons.no_accounts, size: 18),
                          label: const Text('Vacate Room'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                        ),
              ),
          ],
        ),
      );
    } catch (e) {
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.red.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withAlpha(13),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 22),
                const SizedBox(width: 12),
                Flexible(
                  child: Text(
                    'Error loading room details: $e',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.red.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }
  }

  Widget _buildRoomDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Expanded(
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.blue.shade400),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _vacateRoom(String roomId) async {
    // Show confirmation dialog
    final roomName = _currentRoom?.name ?? 'this room';
    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: const Text('Vacate Room'),
            content: Text(
              'Are you sure you want to vacate $roomName? This will mark the tenant as "Moved Out" and make the room available for new tenants.',
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext),
                child: Text(
                  'CANCEL',
                  style: TextStyle(color: Colors.grey.shade700),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  // Close dialog first
                  Navigator.pop(dialogContext);
                  // Then call separate method to handle the async operation
                  _performVacateRoom(roomId);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('VACATE'),
              ),
            ],
          ),
    );
  }

  Future<void> _performVacateRoom(String roomId) async {
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final roomName = _currentRoom?.name ?? 'the room';
    setState(() {
      _isLoading = true;
    });
    try {
      final updatedTenant = widget.tenant.copyWith(
        roomId: null,
        status: TenantStatus.movedOut,
      );
      await serviceLocator.tenantService.updateTenant(updatedTenant);
      await serviceLocator.roomService.updateRoomOccupancyStatus(
        roomId,
        RoomOccupancyStatus.vacant,
      );

      // Clear the current room since tenant has moved out
      setState(() {
        _currentRoom = null;
        _isLoading = false;
      });

      if (!mounted) return;
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Vacated $roomName successfully'),
          backgroundColor: Colors.green,
        ),
      );
      navigator.pop(updatedTenant);
    } catch (e) {
      if (!mounted) return;
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error vacating $roomName: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Method to handle room assignment
  Future<void> _assignRoom() async {
    // Capture navigator and scaffold messenger before async operations
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Navigate to AssignRoomPage and wait for result
      final result = await navigator.push(
        MaterialPageRoute(
          builder: (context) => AssignRoomPage(tenant: widget.tenant),
        ),
      );

      if (result != null && result is Tenant) {
        // Room was assigned, refresh the tenant details
        if (mounted) {
          // If tenant was previously moved out, their status should now be active
          if (widget.tenant.status == TenantStatus.movedOut) {
            // Update tenant status to active
            await serviceLocator.tenantService.updateTenantStatus(
              result.id,
              TenantStatus.active,
            );

            // Show specific message for reactivated tenant
            scaffoldMessenger.showSnackBar(
              const SnackBar(
                content: Text(
                  'Tenant reactivated and room assigned successfully',
                ),
                behavior: SnackBarBehavior.floating,
                backgroundColor: Colors.green,
              ),
            );
          } else {
            // Show standard success message
            scaffoldMessenger.showSnackBar(
              const SnackBar(
                content: Text('Room assigned successfully'),
                behavior: SnackBarBehavior.floating,
                backgroundColor: Colors.green,
              ),
            );
          }

          // Return to previous screen with updated tenant
          navigator.pop(result);
        }
      }
    } catch (e) {
      if (mounted) {
        // Show error message
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error assigning room: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _TenantHistoryTab extends StatelessWidget {
  final String tenantId;
  const _TenantHistoryTab({required this.tenantId});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<ActivityLog>>(
      future: serviceLocator.activityLogService.getLogsByTenant(tenantId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text('Error loading history'));
        }
        final logs = snapshot.data ?? [];
        if (logs.isEmpty) {
          return const Center(child: Text('No history found.'));
        }
        return ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: logs.length,
          separatorBuilder: (context, index) => const Divider(),
          itemBuilder: (context, index) {
            final log = logs[index];
            return ListTile(
              leading: Icon(Icons.event_note, color: Colors.blue.shade400),
              title: Text(log.action),
              subtitle: Text(
                log.createdAt.toLocal().toString().split('.').first,
                style: const TextStyle(fontSize: 12),
              ),
              trailing:
                  log.details != null ? const Icon(Icons.info_outline) : null,
              onTap:
                  log.details != null
                      ? () => showDialog(
                        context: context,
                        builder:
                            (ctx) => AlertDialog(
                              title: const Text('Details'),
                              content: Text(log.details.toString()),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(ctx),
                                  child: const Text('Close'),
                                ),
                              ],
                            ),
                      )
                      : null,
            );
          },
        );
      },
    );
  }
}

class _TenantBillsTab extends StatefulWidget {
  final String tenantId;

  const _TenantBillsTab({required this.tenantId});

  @override
  State<_TenantBillsTab> createState() => _TenantBillsTabState();
}

class _TenantBillsTabState extends State<_TenantBillsTab> {
  bool _isLoading = true;
  List<Bill> _bills = [];
  String _filterStatus = 'all';

  @override
  void initState() {
    super.initState();
    _loadBills();
  }

  Future<void> _loadBills() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load bills for this tenant
      final bills = await serviceLocator.billService.getBillsByTenant(
        widget.tenantId,
      );

      // Apply status filter if needed
      final filteredBills =
          _filterStatus == 'all'
              ? bills
              : bills.where((bill) {
                if (_filterStatus == 'overdue') {
                  return bill.isOverdue();
                } else {
                  return bill.status.name == _filterStatus;
                }
              }).toList();

      setState(() {
        _bills = filteredBills;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading bills: $e')));
      }
      setState(() {
        _bills = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildFilterBar(),
        Expanded(
          child:
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _bills.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.receipt_long,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No bills found',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            // Navigate to add bill page
                            Navigator.of(context)
                                .push(
                                  MaterialPageRoute(
                                    builder:
                                        (context) => AddBillPage(
                                          bill: null,
                                          initialTenantId: widget.tenantId,
                                        ),
                                  ),
                                )
                                .then((value) {
                                  if (value == true) {
                                    _loadBills();
                                  }
                                });
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Create Bill'),
                        ),
                      ],
                    ),
                  )
                  : _buildBillsList(),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            onPressed: () {
              // Navigate to add bill page
              Navigator.of(context)
                  .push(
                    MaterialPageRoute(
                      builder:
                          (context) => AddBillPage(
                            bill: null,
                            initialTenantId: widget.tenantId,
                          ),
                    ),
                  )
                  .then((value) {
                    if (value == true) {
                      _loadBills();
                    }
                  });
            },
            icon: const Icon(Icons.add),
            label: const Text('Add New Bill'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: Text('Bills', style: Theme.of(context).textTheme.titleLarge),
          ),
          DropdownButton<String>(
            value: _filterStatus,
            items: const [
              DropdownMenuItem(value: 'all', child: Text('All')),
              DropdownMenuItem(value: 'pending', child: Text('Pending')),
              DropdownMenuItem(value: 'paid', child: Text('Paid')),
              DropdownMenuItem(value: 'overdue', child: Text('Overdue')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _filterStatus = value;
                });
                _loadBills();
              }
            },
          ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadBills),
        ],
      ),
    );
  }

  Widget _buildBillsList() {
    return ListView.builder(
      itemCount: _bills.length,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, index) {
        final bill = _bills[index];
        return _buildBillCard(bill);
      },
    );
  }

  Widget _buildBillCard(Bill bill) {
    final settingsService = serviceLocator.settingsService;
    final isOverdue = bill.isOverdue();

    Color statusColor;
    switch (bill.status) {
      case BillStatus.paid:
        statusColor = Colors.green;
        break;
      case BillStatus.pending:
        statusColor = isOverdue ? Colors.red : Colors.orange;
        break;
      case BillStatus.overdue:
        statusColor = Colors.red;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.of(context)
              .push(
                MaterialPageRoute(
                  builder: (context) => BillDetailPage(billId: bill.id),
                ),
              )
              .then((value) {
                if (value == true) {
                  _loadBills();
                }
              });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      bill.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(26), // 0.1 * 255 = ~26
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: statusColor),
                    ),
                    child: Text(
                      isOverdue && bill.status != BillStatus.paid
                          ? 'OVERDUE'
                          : bill.status.name.toUpperCase(),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                bill.description,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: Colors.grey.shade700),
              ),
              const SizedBox(height: 12),
              if (bill.billNumber != null && bill.billNumber!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Row(
                    children: [
                      Text(
                        'Bill #: ${bill.billNumber}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 8),
                      InkWell(
                        child:
                            const Icon(Icons.copy, size: 16, color: Colors.grey),
                        onTap: () {
                          Clipboard.setData(ClipboardData(text: bill.billNumber!));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content:
                                    Text('Bill number copied to clipboard')),
                          );
                        },
                      )
                    ],
                  ),
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        settingsService.formatCurrency(bill.amount),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (bill.paidAmount != null &&
                          bill.paidAmount! > 0 &&
                          bill.paidAmount! < bill.amount)
                        Text(
                          'Paid: ${settingsService.formatCurrency(bill.paidAmount!)}',
                          style: const TextStyle(
                            color: Colors.green,
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.event,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            DateFormat.yMd().format(bill.dueDate),
                            style: TextStyle(
                              color:
                                  isOverdue && bill.status != BillStatus.paid
                                      ? Colors.red
                                      : Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        bill.type.name.toUpperCase(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
